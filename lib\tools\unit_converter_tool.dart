import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

enum UnitCategory {
  length,
  weight,
  temperature,
  area,
  volume,
  time,
  speed,
  pressure,
  energy,
  currency,
}

class UnitConverterTool extends StatefulWidget {
  const UnitConverterTool({super.key});

  @override
  State<UnitConverterTool> createState() => _UnitConverterToolState();
}

class _UnitConverterToolState extends State<UnitConverterTool> {
  UnitCategory _selectedCategory = UnitCategory.length;
  final TextEditingController _inputController = TextEditingController();
  String _result = '';
  String _fromUnit = '';
  String _toUnit = '';
  bool _isConverting = false;
  String? _errorMessage;

  // Conversion rates and units for each category
  final Map<UnitCategory, Map<String, dynamic>> _conversionData = {
    UnitCategory.length: {
      'units': {
        'Meter': 1.0,
        'Kilometer': 0.001,
        'Centimeter': 100.0,
        'Millimeter': 1000.0,
        'Mile': 0.000621371,
        'Yard': 1.09361,
        'Foot': 3.28084,
        'Inch': 39.3701,
      },
      'defaultFrom': 'Meter',
      'defaultTo': 'Foot',
    },
    UnitCategory.weight: {
      'units': {
        'Kilogram': 1.0,
        'Gram': 1000.0,
        'Milligram': 1000000.0,
        'Metric Ton': 0.001,
        'Pound': 2.20462,
        'Ounce': 35.274,
        'Stone': 0.157473,
      },
      'defaultFrom': 'Kilogram',
      'defaultTo': 'Pound',
    },
    UnitCategory.temperature: {
      'units': {'Celsius': 'C', 'Fahrenheit': 'F', 'Kelvin': 'K'},
      'defaultFrom': 'Celsius',
      'defaultTo': 'Fahrenheit',
    },
    UnitCategory.area: {
      'units': {
        'Square Meter': 1.0,
        'Square Kilometer': 0.000001,
        'Square Centimeter': 10000.0,
        'Square Millimeter': 1000000.0,
        'Square Mile': 3.861e-7,
        'Square Yard': 1.19599,
        'Square Foot': 10.7639,
        'Square Inch': 1550.0,
        'Acre': 0.000247105,
        'Hectare': 0.0001,
      },
      'defaultFrom': 'Square Meter',
      'defaultTo': 'Square Foot',
    },
    UnitCategory.volume: {
      'units': {
        'Cubic Meter': 1.0,
        'Liter': 1000.0,
        'Milliliter': 1000000.0,
        'Gallon (US)': 264.172,
        'Quart (US)': 1056.69,
        'Pint (US)': 2113.38,
        'Cup (US)': 4226.75,
        'Fluid Ounce (US)': 33814.0,
        'Tablespoon (US)': 67628.0,
        'Teaspoon (US)': 202884.0,
      },
      'defaultFrom': 'Liter',
      'defaultTo': 'Gallon (US)',
    },
    UnitCategory.time: {
      'units': {
        'Second': 1.0,
        'Millisecond': 1000.0,
        'Microsecond': 1000000.0,
        'Nanosecond': 1000000000.0,
        'Minute': 1 / 60.0,
        'Hour': 1 / 3600.0,
        'Day': 1 / 86400.0,
        'Week': 1 / 604800.0,
        'Month (30 days)': 1 / 2592000.0,
        'Year (365 days)': 1 / 31536000.0,
      },
      'defaultFrom': 'Minute',
      'defaultTo': 'Hour',
    },
    UnitCategory.speed: {
      'units': {
        'Meter per second': 1.0,
        'Kilometer per hour': 3.6,
        'Mile per hour': 2.23694,
        'Foot per second': 3.28084,
        'Knot': 1.94384,
      },
      'defaultFrom': 'Kilometer per hour',
      'defaultTo': 'Mile per hour',
    },
    UnitCategory.pressure: {
      'units': {
        'Pascal': 1.0,
        'Kilopascal': 0.001,
        'Bar': 0.00001,
        'PSI': 0.000145038,
        'Atmosphere': 9.86923e-6,
        'Torr': 0.00750062,
      },
      'defaultFrom': 'Bar',
      'defaultTo': 'PSI',
    },
    UnitCategory.energy: {
      'units': {
        'Joule': 1.0,
        'Kilojoule': 0.001,
        'Calorie': 0.239006,
        'Kilocalorie': 0.000239006,
        'Watt-hour': 0.000277778,
        'Kilowatt-hour': 2.77778e-7,
        'Electronvolt': 6.242e+18,
        'British Thermal Unit': 0.000947817,
        'Foot-pound': 0.737562,
      },
      'defaultFrom': 'Kilojoule',
      'defaultTo': 'Kilocalorie',
    },
    UnitCategory.currency: {
      'units': {
        'USD': 1.0,
        'EUR': 0.92,
        'GBP': 0.78,
        'JPY': 150.0,
        'CAD': 1.35,
        'AUD': 1.52,
        'CHF': 0.89,
        'CNY': 7.25,
        'INR': 83.0,
        'BRL': 5.05,
      },
      'defaultFrom': 'USD',
      'defaultTo': 'EUR',
    },
  };

  @override
  void initState() {
    super.initState();
    _initializeUnits();
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  // Initialize the default units for the selected category
  void _initializeUnits() {
    final categoryData = _conversionData[_selectedCategory]!;
    _fromUnit = categoryData['defaultFrom'] as String;
    _toUnit = categoryData['defaultTo'] as String;
    _inputController.text = '1';
    _convert();
  }

  // Convert between units
  void _convert() {
    setState(() {
      _isConverting = true;
      _errorMessage = null;
      _result = '';
    });

    try {
      final input = double.parse(_inputController.text);
      double result;

      if (_selectedCategory == UnitCategory.temperature) {
        result = _convertTemperature(input, _fromUnit, _toUnit);
      } else {
        final fromRate = _getConversionRate(_fromUnit);
        final toRate = _getConversionRate(_toUnit);
        result = input * (toRate / fromRate);
      }

      // Format the result
      String formattedResult;
      if (result.abs() < 0.000001 || result.abs() > 1000000) {
        formattedResult = result.toStringAsExponential(6);
      } else {
        formattedResult = result.toStringAsFixed(6);
        // Remove trailing zeros
        if (formattedResult.contains('.')) {
          formattedResult = formattedResult.replaceAll(RegExp(r'0+$'), '');
          formattedResult = formattedResult.replaceAll(RegExp(r'\.$'), '');
        }
      }

      setState(() {
        _result = formattedResult;
        _isConverting = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid input: Please enter a valid number';
        _isConverting = false;
      });
    }
  }

  // Get the conversion rate for a unit
  double _getConversionRate(String unit) {
    final units =
        _conversionData[_selectedCategory]!['units'] as Map<String, dynamic>;
    return units[unit] as double;
  }

  // Convert temperature between different units
  double _convertTemperature(double value, String fromUnit, String toUnit) {
    // First convert to Celsius
    double celsius;
    switch (fromUnit) {
      case 'Celsius':
        celsius = value;
        break;
      case 'Fahrenheit':
        celsius = (value - 32) * 5 / 9;
        break;
      case 'Kelvin':
        celsius = value - 273.15;
        break;
      default:
        throw Exception('Unknown temperature unit: $fromUnit');
    }

    // Then convert from Celsius to the target unit
    switch (toUnit) {
      case 'Celsius':
        return celsius;
      case 'Fahrenheit':
        return celsius * 9 / 5 + 32;
      case 'Kelvin':
        return celsius + 273.15;
      default:
        throw Exception('Unknown temperature unit: $toUnit');
    }
  }

  // Get the category name
  String _getCategoryName(UnitCategory category) {
    switch (category) {
      case UnitCategory.length:
        return 'Length';
      case UnitCategory.weight:
        return 'Weight';
      case UnitCategory.temperature:
        return 'Temperature';
      case UnitCategory.area:
        return 'Area';
      case UnitCategory.volume:
        return 'Volume';
      case UnitCategory.time:
        return 'Time';
      case UnitCategory.speed:
        return 'Speed';
      case UnitCategory.pressure:
        return 'Pressure';
      case UnitCategory.energy:
        return 'Energy';
      case UnitCategory.currency:
        return 'Currency';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Category selector
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Select Category', style: theme.textTheme.titleMedium),
              const SizedBox(height: 8),
              SizedBox(
                height: 50,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: UnitCategory.values.map((category) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: ChoiceChip(
                        label: Text(_getCategoryName(category)),
                        selected: _selectedCategory == category,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedCategory = category;
                              _initializeUnits();
                            });
                          }
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),

        // Converter
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Input
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Input value
                    Expanded(
                      flex: 2,
                      child: TextField(
                        controller: _inputController,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        decoration: InputDecoration(
                          labelText: 'Value',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _inputController.clear();
                            },
                          ),
                        ),
                        onChanged: (_) => _convert(),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // From unit
                    Expanded(
                      flex: 3,
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'From',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          isCollapsed: true,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 14,
                          ),
                        ),
                        value: _fromUnit,
                        isExpanded: true,
                        isDense: true,
                        menuMaxHeight: 300,
                        items:
                            (_conversionData[_selectedCategory]!['units']
                                    as Map<String, dynamic>)
                                .keys
                                .map(
                                  (unit) => DropdownMenuItem(
                                    value: unit,
                                    child: Text(
                                      unit,
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                )
                                .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _fromUnit = value;
                              _convert();
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),

                // Swap button
                IconButton(
                  icon: const Icon(Icons.swap_vert),
                  onPressed: () {
                    setState(() {
                      final temp = _fromUnit;
                      _fromUnit = _toUnit;
                      _toUnit = temp;
                      _convert();
                    });
                  },
                  tooltip: 'Swap units',
                ),

                // Output
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Result
                    Expanded(
                      flex: 2,
                      child: Container(
                        height: 60,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: theme.colorScheme.outline.withAlpha(100),
                          ),
                        ),
                        child: Center(
                          child: _isConverting
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(
                                  _result,
                                  style: theme.textTheme.titleMedium,
                                  textAlign: TextAlign.center,
                                ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // To unit
                    Expanded(
                      flex: 3,
                      child: DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'To',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          isCollapsed: true,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 14,
                          ),
                        ),
                        value: _toUnit,
                        isExpanded: true,
                        isDense: true,
                        menuMaxHeight: 300,
                        items:
                            (_conversionData[_selectedCategory]!['units']
                                    as Map<String, dynamic>)
                                .keys
                                .map(
                                  (unit) => DropdownMenuItem(
                                    value: unit,
                                    child: Text(
                                      unit,
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(fontSize: 14),
                                    ),
                                  ),
                                )
                                .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _toUnit = value;
                              _convert();
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),

                // Copy button
                if (_result.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: OutlinedButton.icon(
                      onPressed: () {
                        Clipboard.setData(
                          ClipboardData(
                            text:
                                '$_inputController $_fromUnit = $_result $_toUnit',
                          ),
                        );
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Conversion copied to clipboard'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                      icon: const Icon(Icons.copy),
                      label: const Text('Copy Result'),
                    ),
                  ),

                // Error message
                if (_errorMessage != null)
                  Container(
                    margin: const EdgeInsets.only(top: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(30),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 20,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(
                            Icons.close,
                            color: Colors.red,
                            size: 20,
                          ),
                          onPressed: () {
                            setState(() {
                              _errorMessage = null;
                            });
                          },
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          tooltip: 'Dismiss',
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
