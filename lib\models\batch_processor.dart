import 'package:flutter/foundation.dart';

enum BatchProcessStatus {
  idle,
  processing,
  completed,
  error,
}

class BatchProcessItem {
  final String id;
  final String name;
  final String path;
  final String type;
  final int size;
  bool isSelected;
  bool isProcessed;
  String? outputPath;
  String? errorMessage;

  BatchProcessItem({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.size,
    this.isSelected = true,
    this.isProcessed = false,
    this.outputPath,
    this.errorMessage,
  });

  BatchProcessItem copyWith({
    String? id,
    String? name,
    String? path,
    String? type,
    int? size,
    bool? isSelected,
    bool? isProcessed,
    String? outputPath,
    String? errorMessage,
  }) {
    return BatchProcessItem(
      id: id ?? this.id,
      name: name ?? this.name,
      path: path ?? this.path,
      type: type ?? this.type,
      size: size ?? this.size,
      isSelected: isSelected ?? this.isSelected,
      isProcessed: isProcessed ?? this.isProcessed,
      outputPath: outputPath ?? this.outputPath,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class BatchProcessor with ChangeNotifier {
  final List<BatchProcessItem> _items = [];
  BatchProcessStatus _status = BatchProcessStatus.idle;
  int _processedCount = 0;
  String _toolId = '';
  String _outputDirectory = '';
  
  // Getters
  List<BatchProcessItem> get items => [..._items];
  BatchProcessStatus get status => _status;
  int get processedCount => _processedCount;
  int get totalCount => _items.length;
  int get selectedCount => _items.where((item) => item.isSelected).length;
  double get progress => _items.isEmpty ? 0 : _processedCount / selectedCount;
  bool get isProcessing => _status == BatchProcessStatus.processing;
  String get toolId => _toolId;
  String get outputDirectory => _outputDirectory;
  
  // Add items to batch
  void addItems(List<BatchProcessItem> items, String toolId) {
    _items.addAll(items);
    _toolId = toolId;
    notifyListeners();
  }
  
  // Clear all items
  void clearItems() {
    _items.clear();
    _processedCount = 0;
    _status = BatchProcessStatus.idle;
    notifyListeners();
  }
  
  // Toggle item selection
  void toggleItemSelection(String id) {
    final index = _items.indexWhere((item) => item.id == id);
    if (index >= 0) {
      _items[index] = _items[index].copyWith(isSelected: !_items[index].isSelected);
      notifyListeners();
    }
  }
  
  // Select all items
  void selectAllItems() {
    for (int i = 0; i < _items.length; i++) {
      _items[i] = _items[i].copyWith(isSelected: true);
    }
    notifyListeners();
  }
  
  // Deselect all items
  void deselectAllItems() {
    for (int i = 0; i < _items.length; i++) {
      _items[i] = _items[i].copyWith(isSelected: false);
    }
    notifyListeners();
  }
  
  // Set output directory
  void setOutputDirectory(String path) {
    _outputDirectory = path;
    notifyListeners();
  }
  
  // Start processing
  void startProcessing() {
    if (_items.isEmpty || _status == BatchProcessStatus.processing) return;
    
    _status = BatchProcessStatus.processing;
    _processedCount = 0;
    
    // Reset processed status
    for (int i = 0; i < _items.length; i++) {
      if (_items[i].isSelected) {
        _items[i] = _items[i].copyWith(
          isProcessed: false,
          outputPath: null,
          errorMessage: null,
        );
      }
    }
    
    notifyListeners();
    
    // In a real app, we would process the items here
    // For now, we'll just simulate processing
    _simulateProcessing();
  }
  
  // Cancel processing
  void cancelProcessing() {
    if (_status != BatchProcessStatus.processing) return;
    
    _status = BatchProcessStatus.idle;
    notifyListeners();
  }
  
  // Simulate processing (for demo purposes)
  Future<void> _simulateProcessing() async {
    final selectedItems = _items.where((item) => item.isSelected).toList();
    
    for (int i = 0; i < selectedItems.length; i++) {
      if (_status != BatchProcessStatus.processing) break;
      
      // Find the item in the main list
      final index = _items.indexWhere((item) => item.id == selectedItems[i].id);
      
      // Simulate processing delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Update the item
      if (index >= 0) {
        final success = i % 5 != 0; // Simulate occasional failures
        
        _items[index] = _items[index].copyWith(
          isProcessed: true,
          outputPath: success ? '$_outputDirectory/${_items[index].name}' : null,
          errorMessage: success ? null : 'Failed to process file',
        );
        
        _processedCount++;
        notifyListeners();
      }
    }
    
    _status = BatchProcessStatus.completed;
    notifyListeners();
  }
}
