import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:smart_toolbox/models/batch_processor.dart';
import 'package:smart_toolbox/models/tool.dart';
import 'package:smart_toolbox/utils/tool_icons.dart';
import 'package:uuid/uuid.dart';

class BatchProcessScreen extends StatefulWidget {
  final Tool tool;
  final List<String> filePaths;
  
  const BatchProcessScreen({
    super.key,
    required this.tool,
    required this.filePaths,
  });

  @override
  State<BatchProcessScreen> createState() => _BatchProcessScreenState();
}

class _BatchProcessScreenState extends State<BatchProcessScreen> {
  bool _isInitialized = false;
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    
    if (!_isInitialized) {
      _initializeBatchProcessor();
      _isInitialized = true;
    }
  }
  
  void _initializeBatchProcessor() {
    final batchProcessor = Provider.of<BatchProcessor>(context, listen: false);
    
    // Clear any existing items
    batchProcessor.clearItems();
    
    // Add items from file paths
    final items = widget.filePaths.map((path) {
      final name = path.split('/').last;
      final type = name.split('.').last.toLowerCase();
      
      return BatchProcessItem(
        id: const Uuid().v4(),
        name: name,
        path: path,
        type: type,
        size: 1024 * 1024, // Placeholder size (1MB)
      );
    }).toList();
    
    batchProcessor.addItems(items, widget.tool.id);
    batchProcessor.setOutputDirectory('/storage/emulated/0/Download/SmartToolbox');
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final batchProcessor = Provider.of<BatchProcessor>(context);
    final items = batchProcessor.items;
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Batch ${widget.tool.name}'),
        actions: [
          if (!batchProcessor.isProcessing)
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'select_all') {
                  batchProcessor.selectAllItems();
                } else if (value == 'deselect_all') {
                  batchProcessor.deselectAllItems();
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'select_all',
                  child: Text('Select All'),
                ),
                const PopupMenuItem(
                  value: 'deselect_all',
                  child: Text('Deselect All'),
                ),
              ],
            ),
        ],
      ),
      body: Column(
        children: [
          // Tool info card
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withAlpha(30),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        ToolIcons.getIconById(widget.tool.id),
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.tool.name,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.tool.description,
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Progress indicator
          if (batchProcessor.isProcessing)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                children: [
                  LinearProgressIndicator(
                    value: batchProcessor.progress,
                    backgroundColor: theme.colorScheme.primary.withAlpha(30),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Processing ${batchProcessor.processedCount} of ${batchProcessor.selectedCount} files...',
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          
          // File list
          Expanded(
            child: items.isEmpty
                ? Center(
                    child: Text(
                      'No files to process',
                      style: theme.textTheme.titleMedium,
                    ),
                  )
                : ListView.builder(
                    itemCount: items.length,
                    itemBuilder: (context, index) {
                      final item = items[index];
                      
                      return ListTile(
                        leading: Stack(
                          alignment: Alignment.center,
                          children: [
                            Icon(
                              _getFileTypeIcon(item.type),
                              color: theme.colorScheme.primary,
                            ),
                            if (item.isProcessed)
                              Positioned(
                                right: 0,
                                bottom: 0,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: item.errorMessage == null
                                        ? Colors.green
                                        : Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    item.errorMessage == null
                                        ? Icons.check
                                        : Icons.error,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        title: Text(
                          item.name,
                          style: TextStyle(
                            decoration: item.isProcessed && item.errorMessage == null
                                ? TextDecoration.lineThrough
                                : null,
                          ),
                        ),
                        subtitle: item.errorMessage != null
                            ? Text(
                                item.errorMessage!,
                                style: TextStyle(color: Colors.red),
                              )
                            : Text(_formatFileSize(item.size)),
                        trailing: !batchProcessor.isProcessing
                            ? Checkbox(
                                value: item.isSelected,
                                onChanged: (value) {
                                  batchProcessor.toggleItemSelection(item.id);
                                },
                              )
                            : null,
                      );
                    },
                  ),
          ),
        ],
      ),
      bottomNavigationBar: BottomAppBar(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${batchProcessor.selectedCount} files selected',
              style: theme.textTheme.bodyMedium,
            ),
            ElevatedButton(
              onPressed: batchProcessor.isProcessing
                  ? () => batchProcessor.cancelProcessing()
                  : batchProcessor.selectedCount > 0
                      ? () => batchProcessor.startProcessing()
                      : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: batchProcessor.isProcessing
                    ? Colors.red
                    : theme.colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                batchProcessor.isProcessing ? 'Cancel' : 'Process',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  IconData _getFileTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
        return Icons.image;
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'mp3':
      case 'wav':
      case 'ogg':
        return Icons.audio_file;
      case 'mp4':
      case 'mov':
      case 'avi':
        return Icons.video_file;
      default:
        return Icons.insert_drive_file;
    }
  }
  
  String _formatFileSize(int size) {
    if (size < 1024) {
      return '$size B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
