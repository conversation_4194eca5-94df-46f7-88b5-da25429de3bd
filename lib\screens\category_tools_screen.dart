import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:smart_toolbox/models/category.dart';
import 'package:smart_toolbox/models/data_provider.dart';
import 'package:smart_toolbox/screens/tool_detail_screen.dart';
import 'package:smart_toolbox/widgets/tool_card.dart';

class CategoryToolsScreen extends StatelessWidget {
  final Category category;

  const CategoryToolsScreen({
    super.key,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<DataProvider>(context);
    final tools = dataProvider.getToolsByCategory(category.id);
    final color = Color(int.parse(category.color.replaceAll('#', '0xFF')));
    
    return Scaffold(
      appBar: AppBar(
        title: Text(category.name),
        backgroundColor: color,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              category.description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            Expanded(
              child: tools.isEmpty
                  ? Center(
                      child: Text(
                        'No tools available in this category yet.',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    )
                  : MasonryGridView.count(
                      crossAxisCount: 2,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      itemCount: tools.length,
                      itemBuilder: (context, index) {
                        return ToolCard(
                          tool: tools[index],
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ToolDetailScreen(
                                  tool: tools[index],
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
