import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:path/path.dart' as path;

enum QrDataType {
  text,
  url,
  email,
  phone,
  sms,
  wifi,
  location,
}

class QrCodeTool extends StatefulWidget {
  const QrCodeTool({super.key});

  @override
  State<QrCodeTool> createState() => _QrCodeToolState();
}

class _QrCodeToolState extends State<QrCodeTool> {
  final TextEditingController _dataController = TextEditingController();
  final TextEditingController _wifiNameController = TextEditingController();
  final TextEditingController _wifiPasswordController = TextEditingController();
  final TextEditingController _emailSubjectController = TextEditingController();
  final TextEditingController _smsMessageController = TextEditingController();
  final TextEditingController _latitudeController = TextEditingController();
  final TextEditingController _longitudeController = TextEditingController();
  
  QrDataType _selectedDataType = QrDataType.text;
  bool _isWifiEncrypted = true;
  String _wifiEncryptionType = 'WPA';
  bool _isGenerating = false;
  String? _generatedQrPath;
  String? _errorMessage;
  
  final GlobalKey _qrKey = GlobalKey();
  
  @override
  void dispose() {
    _dataController.dispose();
    _wifiNameController.dispose();
    _wifiPasswordController.dispose();
    _emailSubjectController.dispose();
    _smsMessageController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Data type selector
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'QR Code Type',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: QrDataType.values.map((type) {
                  return ChoiceChip(
                    label: Text(_getDataTypeLabel(type)),
                    selected: _selectedDataType == type,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedDataType = type;
                        });
                      }
                    },
                  );
                }).toList(),
              ),
            ],
          ),
        ),
        
        // Data input fields
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: _buildDataInputFields(),
        ),
        
        const SizedBox(height: 16),
        
        // Generate button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ElevatedButton.icon(
            onPressed: _isGenerating ? null : _generateQrCode,
            icon: _isGenerating
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.qr_code),
            label: Text(_isGenerating ? 'Generating...' : 'Generate QR Code'),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // QR Code preview
        Expanded(
          child: Center(
            child: _getQrData().isNotEmpty
                ? RepaintBoundary(
                    key: _qrKey,
                    child: Container(
                      color: Colors.white,
                      padding: const EdgeInsets.all(16),
                      child: QrImageView(
                        data: _getQrData(),
                        version: QrVersions.auto,
                        size: 200,
                        backgroundColor: Colors.white,
                        embeddedImage: const AssetImage('assets/icons/app_icon.png'),
                        embeddedImageStyle: const QrEmbeddedImageStyle(
                          size: Size(40, 40),
                        ),
                      ),
                    ),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.qr_code,
                        size: 64,
                        color: theme.colorScheme.primary.withAlpha(100),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Enter data to generate QR code',
                        style: theme.textTheme.titleMedium,
                      ),
                    ],
                  ),
          ),
        ),
        
        // Actions
        if (_getQrData().isNotEmpty)
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                OutlinedButton.icon(
                  onPressed: _saveQrCode,
                  icon: const Icon(Icons.save_alt),
                  label: const Text('Save'),
                ),
                ElevatedButton.icon(
                  onPressed: _copyQrData,
                  icon: const Icon(Icons.copy),
                  label: const Text('Copy Data'),
                ),
              ],
            ),
          ),
        
        // Error message
        if (_errorMessage != null)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withAlpha(30),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.red),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
                IconButton(
                  icon: const Icon(
                    Icons.close,
                    color: Colors.red,
                  ),
                  onPressed: () {
                    setState(() {
                      _errorMessage = null;
                    });
                  },
                ),
              ],
            ),
          ),
      ],
    );
  }
  
  // Build data input fields based on selected data type
  Widget _buildDataInputFields() {
    switch (_selectedDataType) {
      case QrDataType.text:
        return TextField(
          controller: _dataController,
          decoration: const InputDecoration(
            labelText: 'Text',
            hintText: 'Enter text to encode',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        );
      
      case QrDataType.url:
        return TextField(
          controller: _dataController,
          decoration: const InputDecoration(
            labelText: 'URL',
            hintText: 'https://example.com',
            border: OutlineInputBorder(),
            prefixText: 'https://',
          ),
          keyboardType: TextInputType.url,
        );
      
      case QrDataType.email:
        return Column(
          children: [
            TextField(
              controller: _dataController,
              decoration: const InputDecoration(
                labelText: 'Email Address',
                hintText: '<EMAIL>',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _emailSubjectController,
              decoration: const InputDecoration(
                labelText: 'Subject (Optional)',
                hintText: 'Email subject',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        );
      
      case QrDataType.phone:
        return TextField(
          controller: _dataController,
          decoration: const InputDecoration(
            labelText: 'Phone Number',
            hintText: '+1234567890',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.phone,
        );
      
      case QrDataType.sms:
        return Column(
          children: [
            TextField(
              controller: _dataController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                hintText: '+1234567890',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _smsMessageController,
              decoration: const InputDecoration(
                labelText: 'Message (Optional)',
                hintText: 'SMS message',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        );
      
      case QrDataType.wifi:
        return Column(
          children: [
            TextField(
              controller: _wifiNameController,
              decoration: const InputDecoration(
                labelText: 'Network Name (SSID)',
                hintText: 'WiFi network name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Encrypted'),
              value: _isWifiEncrypted,
              onChanged: (value) {
                setState(() {
                  _isWifiEncrypted = value;
                });
              },
            ),
            if (_isWifiEncrypted) ...[
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Encryption Type',
                  border: OutlineInputBorder(),
                ),
                value: _wifiEncryptionType,
                items: ['WPA', 'WEP', 'WPA2-EAP'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _wifiEncryptionType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _wifiPasswordController,
                decoration: const InputDecoration(
                  labelText: 'Password',
                  hintText: 'WiFi password',
                  border: OutlineInputBorder(),
                ),
                obscureText: true,
              ),
            ],
          ],
        );
      
      case QrDataType.location:
        return Column(
          children: [
            TextField(
              controller: _latitudeController,
              decoration: const InputDecoration(
                labelText: 'Latitude',
                hintText: '37.4219983',
                border: OutlineInputBorder(),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _longitudeController,
              decoration: const InputDecoration(
                labelText: 'Longitude',
                hintText: '-122.084',
                border: OutlineInputBorder(),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
            ),
          ],
        );
    }
  }
  
  // Get QR data based on selected data type
  String _getQrData() {
    switch (_selectedDataType) {
      case QrDataType.text:
        return _dataController.text;
      
      case QrDataType.url:
        if (_dataController.text.isEmpty) return '';
        return 'https://${_dataController.text}';
      
      case QrDataType.email:
        if (_dataController.text.isEmpty) return '';
        final subject = _emailSubjectController.text.isNotEmpty
            ? '?subject=${Uri.encodeComponent(_emailSubjectController.text)}'
            : '';
        return 'mailto:${_dataController.text}$subject';
      
      case QrDataType.phone:
        if (_dataController.text.isEmpty) return '';
        return 'tel:${_dataController.text}';
      
      case QrDataType.sms:
        if (_dataController.text.isEmpty) return '';
        final message = _smsMessageController.text.isNotEmpty
            ? '?body=${Uri.encodeComponent(_smsMessageController.text)}'
            : '';
        return 'sms:${_dataController.text}$message';
      
      case QrDataType.wifi:
        if (_wifiNameController.text.isEmpty) return '';
        final type = _isWifiEncrypted ? 'T:$_wifiEncryptionType;' : '';
        final password = _isWifiEncrypted ? 'P:${_wifiPasswordController.text};' : '';
        return 'WIFI:S:${_wifiNameController.text};$type$password;';
      
      case QrDataType.location:
        if (_latitudeController.text.isEmpty || _longitudeController.text.isEmpty) {
          return '';
        }
        return 'geo:${_latitudeController.text},${_longitudeController.text}';
    }
  }
  
  // Get data type label
  String _getDataTypeLabel(QrDataType type) {
    switch (type) {
      case QrDataType.text:
        return 'Text';
      case QrDataType.url:
        return 'URL';
      case QrDataType.email:
        return 'Email';
      case QrDataType.phone:
        return 'Phone';
      case QrDataType.sms:
        return 'SMS';
      case QrDataType.wifi:
        return 'WiFi';
      case QrDataType.location:
        return 'Location';
    }
  }
  
  // Generate QR code
  void _generateQrCode() {
    final data = _getQrData();
    if (data.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter data to generate QR code';
      });
      return;
    }
    
    setState(() {
      _errorMessage = null;
    });
  }
  
  // Save QR code
  Future<void> _saveQrCode() async {
    try {
      setState(() {
        _isGenerating = true;
        _errorMessage = null;
      });
      
      // Capture QR code as image
      final boundary = _qrKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        setState(() {
          _errorMessage = 'Failed to capture QR code';
          _isGenerating = false;
        });
        return;
      }
      
      final image = await boundary.toImage(pixelRatio: 3.0);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData == null) {
        setState(() {
          _errorMessage = 'Failed to convert QR code to image';
          _isGenerating = false;
        });
        return;
      }
      
      final pngBytes = byteData.buffer.asUint8List();
      
      // Save to temporary directory
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = path.join(tempDir.path, 'qrcode_$timestamp.png');
      
      final file = File(filePath);
      await file.writeAsBytes(pngBytes);
      
      setState(() {
        _generatedQrPath = filePath;
        _isGenerating = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('QR code saved to: $filePath'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Error saving QR code: $e';
        _isGenerating = false;
      });
    }
  }
  
  // Copy QR data to clipboard
  void _copyQrData() {
    final data = _getQrData();
    if (data.isEmpty) {
      setState(() {
        _errorMessage = 'No data to copy';
      });
      return;
    }
    
    Clipboard.setData(ClipboardData(text: data));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Data copied to clipboard'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
