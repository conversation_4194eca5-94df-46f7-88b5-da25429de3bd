import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:share_plus/share_plus.dart';

class TextNoteTool extends StatefulWidget {
  const TextNoteTool({super.key});

  @override
  State<TextNoteTool> createState() => _TextNoteToolState();
}

class _TextNoteToolState extends State<TextNoteTool> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final List<Map<String, dynamic>> _savedNotes = [];
  bool _isEditing = false;
  int? _editingIndex;
  String? _errorMessage;
  bool _isSaving = false;

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Title and actions
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  _isEditing ? 'Edit Note' : 'New Note',
                  style: theme.textTheme.titleLarge,
                ),
              ),
              if (_isEditing)
                TextButton.icon(
                  onPressed: _cancelEditing,
                  icon: const Icon(Icons.cancel),
                  label: const Text('Cancel'),
                ),
            ],
          ),
        ),

        // Note editor
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                // Title field
                TextField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    labelText: 'Title',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.title),
                  ),
                  maxLines: 1,
                  textCapitalization: TextCapitalization.sentences,
                ),

                const SizedBox(height: 16),

                // Content field
                Expanded(
                  child: TextField(
                    controller: _contentController,
                    decoration: InputDecoration(
                      labelText: 'Content',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      alignLabelWithHint: true,
                    ),
                    maxLines: null,
                    expands: true,
                    textCapitalization: TextCapitalization.sentences,
                    textAlignVertical: TextAlignVertical.top,
                  ),
                ),

                const SizedBox(height: 16),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    OutlinedButton.icon(
                      onPressed: _clearNote,
                      icon: const Icon(Icons.clear_all),
                      label: const Text('Clear'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: _isSaving ? null : _saveNote,
                      icon: _isSaving
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.save),
                      label: Text(_isSaving ? 'Saving...' : 'Save'),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                    ),
                    OutlinedButton.icon(
                      onPressed: _shareNote,
                      icon: const Icon(Icons.share),
                      label: const Text('Share'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        // Saved notes
        if (_savedNotes.isNotEmpty)
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Saved Notes', style: theme.textTheme.titleMedium),
                const SizedBox(height: 8),
                Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _savedNotes.length,
                    itemBuilder: (context, index) {
                      final note = _savedNotes[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Title and actions
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      note['title'] as String,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: Theme.of(
                                        context,
                                      ).textTheme.titleMedium,
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.edit, size: 20),
                                    onPressed: () => _editNote(index),
                                    tooltip: 'Edit',
                                    constraints: const BoxConstraints(),
                                    padding: const EdgeInsets.all(8),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete, size: 20),
                                    onPressed: () => _deleteNote(index),
                                    tooltip: 'Delete',
                                    constraints: const BoxConstraints(),
                                    padding: const EdgeInsets.all(8),
                                  ),
                                ],
                              ),

                              // Content preview
                              Text(
                                note['content'] as String,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),

                              const SizedBox(height: 4),

                              // Location
                              if (note.containsKey('location'))
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.folder,
                                      size: 14,
                                      color: Colors.grey,
                                    ),
                                    const SizedBox(width: 4),
                                    Expanded(
                                      child: Text(
                                        'Saved in: ${note['location']}',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall
                                            ?.copyWith(
                                              color: Colors.grey,
                                              fontSize: 12,
                                            ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),

                              // Make the whole card tappable
                              Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: () => _viewNote(index),
                                  child: const SizedBox(
                                    width: double.infinity,
                                    height: 0,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

        // Error message
        if (_errorMessage != null)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withAlpha(30),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red),
            ),
            child: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 20),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.red, size: 20),
                  onPressed: () {
                    setState(() {
                      _errorMessage = null;
                    });
                  },
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Dismiss',
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Clear the note
  void _clearNote() {
    setState(() {
      _titleController.clear();
      _contentController.clear();
      _isEditing = false;
      _editingIndex = null;
    });
  }

  // Save the note
  Future<void> _saveNote() async {
    final title = _titleController.text.trim();
    final content = _contentController.text.trim();

    if (title.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a title for your note';
      });
      return;
    }

    if (content.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter some content for your note';
      });
      return;
    }

    setState(() {
      _isSaving = true;
      _errorMessage = null;
    });

    try {
      // Get the app's documents directory
      final docsDir = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName =
          '${title.replaceAll(' ', '_').toLowerCase()}_$timestamp.txt';
      final filePath = path.join(docsDir.path, fileName);

      // Save the note to a file
      final file = File(filePath);
      await file.writeAsString('$title\n\n$content');

      // Add to saved notes
      final note = {
        'title': title,
        'content': content,
        'path': filePath,
        'timestamp': timestamp,
        'location': docsDir.path,
      };

      setState(() {
        if (_isEditing && _editingIndex != null) {
          _savedNotes[_editingIndex!] = note;
        } else {
          _savedNotes.add(note);
        }

        _titleController.clear();
        _contentController.clear();
        _isEditing = false;
        _editingIndex = null;
        _isSaving = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Note saved successfully!'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error saving note: $e';
        _isSaving = false;
      });
    }
  }

  // Share the note
  void _shareNote() {
    final title = _titleController.text.trim();
    final content = _contentController.text.trim();

    if (title.isEmpty && content.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter some text to share';
      });
      return;
    }

    final textToShare = title.isNotEmpty && content.isNotEmpty
        ? '$title\n\n$content'
        : title.isNotEmpty
        ? title
        : content;

    Share.share(textToShare, subject: title.isNotEmpty ? title : 'Shared Note');
  }

  // Edit a saved note
  void _editNote(int index) {
    final note = _savedNotes[index];
    setState(() {
      _titleController.text = note['title'] as String;
      _contentController.text = note['content'] as String;
      _isEditing = true;
      _editingIndex = index;
    });
  }

  // Delete a saved note
  void _deleteNote(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Note'),
        content: const Text('Are you sure you want to delete this note?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _savedNotes.removeAt(index);
              });
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  // View a saved note
  void _viewNote(int index) {
    final note = _savedNotes[index];
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(note['title'] as String),
        content: SingleChildScrollView(child: Text(note['content'] as String)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _editNote(index);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  // Cancel editing
  void _cancelEditing() {
    setState(() {
      _titleController.clear();
      _contentController.clear();
      _isEditing = false;
      _editingIndex = null;
    });
  }
}
