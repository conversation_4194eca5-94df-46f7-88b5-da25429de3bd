import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smart_toolbox/models/batch_processor.dart';
import 'package:smart_toolbox/models/cloud_storage_provider.dart';
import 'package:smart_toolbox/models/data_provider.dart';
import 'package:smart_toolbox/models/history_provider.dart';
import 'package:smart_toolbox/models/theme_provider.dart';
import 'package:smart_toolbox/screens/main_screen.dart';
import 'package:smart_toolbox/screens/onboarding_screen.dart';
import 'package:smart_toolbox/theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load preferences
  final prefs = await SharedPreferences.getInstance();
  final isDarkMode = prefs.getBool('darkMode') ?? false;
  final onboardingCompleted = prefs.getBool('onboarding_completed') ?? false;

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => DataProvider()),
        ChangeNotifierProvider(create: (context) => HistoryProvider()),
        ChangeNotifierProvider(
          create: (context) => ThemeProvider(isDarkMode: isDarkMode),
        ),
        ChangeNotifierProvider(create: (context) => BatchProcessor()),
        ChangeNotifierProvider(create: (context) => CloudStorageProvider()),
      ],
      child: MyApp(onboardingCompleted: onboardingCompleted),
    ),
  );
}

class MyApp extends StatelessWidget {
  final bool onboardingCompleted;

  const MyApp({super.key, required this.onboardingCompleted});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return MaterialApp(
      title: 'Smart Toolbox',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeProvider.themeMode,
      home: onboardingCompleted ? const MainScreen() : const OnboardingScreen(),
    );
  }
}
