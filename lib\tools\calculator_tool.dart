import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:math_expressions/math_expressions.dart';

class CalculatorTool extends StatefulWidget {
  const CalculatorTool({super.key});

  @override
  State<CalculatorTool> createState() => _CalculatorToolState();
}

class _CalculatorToolState extends State<CalculatorTool> {
  String _display = '0';
  String _expression = '';
  bool _isScientificMode = false;
  bool _hasCalculated = false;

  // Calculator button colors
  final _numColor = Colors.white;
  final _opColor = const Color(0xFFE5E5E5);
  final _funcColor = const Color(0xFFD4E2FC);
  final _actionColor = const Color(0xFFB8D0F5);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Display
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                _expression,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withAlpha(180),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _display,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.end,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),

        // Mode toggle
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text('Scientific Mode', style: theme.textTheme.bodyMedium),
              const SizedBox(width: 8),
              Switch(
                value: _isScientificMode,
                onChanged: (value) {
                  setState(() {
                    _isScientificMode = value;
                  });
                },
              ),
            ],
          ),
        ),

        // Calculator buttons
        Expanded(
          child: _isScientificMode
              ? _buildScientificKeypad(theme)
              : _buildBasicKeypad(theme),
        ),
      ],
    );
  }

  // Build basic keypad
  Widget _buildBasicKeypad(ThemeData theme) {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              _buildButton('C', _actionColor, _onClearPressed),
              _buildButton('⌫', _actionColor, _onBackspacePressed),
              _buildButton('%', _opColor, () => _onOperatorPressed('%')),
              _buildButton('÷', _opColor, () => _onOperatorPressed('/')),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              _buildButton('7', _numColor, () => _onDigitPressed('7')),
              _buildButton('8', _numColor, () => _onDigitPressed('8')),
              _buildButton('9', _numColor, () => _onDigitPressed('9')),
              _buildButton('×', _opColor, () => _onOperatorPressed('*')),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              _buildButton('4', _numColor, () => _onDigitPressed('4')),
              _buildButton('5', _numColor, () => _onDigitPressed('5')),
              _buildButton('6', _numColor, () => _onDigitPressed('6')),
              _buildButton('-', _opColor, () => _onOperatorPressed('-')),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              _buildButton('1', _numColor, () => _onDigitPressed('1')),
              _buildButton('2', _numColor, () => _onDigitPressed('2')),
              _buildButton('3', _numColor, () => _onDigitPressed('3')),
              _buildButton('+', _opColor, () => _onOperatorPressed('+')),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              _buildButton('±', _numColor, _onToggleSignPressed),
              _buildButton('0', _numColor, () => _onDigitPressed('0')),
              _buildButton('.', _numColor, _onDecimalPressed),
              _buildButton(
                '=',
                theme.colorScheme.primary,
                _onEqualsPressed,
                textColor: Colors.white,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build scientific keypad
  Widget _buildScientificKeypad(ThemeData theme) {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              _buildButton('C', _actionColor, _onClearPressed),
              _buildButton('⌫', _actionColor, _onBackspacePressed),
              _buildButton('(', _funcColor, () => _onDigitPressed('(')),
              _buildButton(')', _funcColor, () => _onDigitPressed(')')),
              _buildButton('÷', _opColor, () => _onOperatorPressed('/')),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              _buildButton('sin', _funcColor, () => _onFunctionPressed('sin')),
              _buildButton('7', _numColor, () => _onDigitPressed('7')),
              _buildButton('8', _numColor, () => _onDigitPressed('8')),
              _buildButton('9', _numColor, () => _onDigitPressed('9')),
              _buildButton('×', _opColor, () => _onOperatorPressed('*')),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              _buildButton('cos', _funcColor, () => _onFunctionPressed('cos')),
              _buildButton('4', _numColor, () => _onDigitPressed('4')),
              _buildButton('5', _numColor, () => _onDigitPressed('5')),
              _buildButton('6', _numColor, () => _onDigitPressed('6')),
              _buildButton('-', _opColor, () => _onOperatorPressed('-')),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              _buildButton('tan', _funcColor, () => _onFunctionPressed('tan')),
              _buildButton('1', _numColor, () => _onDigitPressed('1')),
              _buildButton('2', _numColor, () => _onDigitPressed('2')),
              _buildButton('3', _numColor, () => _onDigitPressed('3')),
              _buildButton('+', _opColor, () => _onOperatorPressed('+')),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              _buildButton('π', _funcColor, () => _onDigitPressed('π')),
              _buildButton('0', _numColor, () => _onDigitPressed('0')),
              _buildButton('.', _numColor, _onDecimalPressed),
              _buildButton('√', _funcColor, () => _onFunctionPressed('sqrt')),
              _buildButton(
                '=',
                theme.colorScheme.primary,
                _onEqualsPressed,
                textColor: Colors.white,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build calculator button
  Widget _buildButton(
    String text,
    Color color,
    VoidCallback onPressed, {
    Color? textColor,
  }) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(4.0),
        child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: textColor ?? Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.zero,
          ),
          child: Text(
            text,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  // Handle digit button press
  void _onDigitPressed(String digit) {
    setState(() {
      if (_hasCalculated) {
        _display = digit;
        _expression = '';
        _hasCalculated = false;
      } else if (_display == '0') {
        _display = digit;
      } else {
        _display += digit;
      }
    });
  }

  // Handle operator button press
  void _onOperatorPressed(String operator) {
    setState(() {
      _expression = '$_display $operator';
      _display = '0';
      _hasCalculated = false;
    });
  }

  // Handle equals button press
  void _onEqualsPressed() {
    setState(() {
      _expression += ' $_display';

      try {
        // Replace π with its value
        String expr = _expression.replaceAll('π', '3.14159265359');

        // Parse and evaluate the expression
        ShuntingYardParser p = ShuntingYardParser();
        Expression exp = p.parse(expr);
        ContextModel cm = ContextModel();
        double result = exp.evaluate(EvaluationType.REAL, cm);

        // Format the result
        if (result == result.toInt()) {
          _display = result.toInt().toString();
        } else {
          _display = result.toString();
        }

        _hasCalculated = true;
      } catch (e) {
        _display = 'Error';
      }
    });
  }

  // Handle clear button press
  void _onClearPressed() {
    setState(() {
      _display = '0';
      _expression = '';
      _hasCalculated = false;
    });
  }

  // Handle backspace button press
  void _onBackspacePressed() {
    setState(() {
      if (_display.length > 1) {
        _display = _display.substring(0, _display.length - 1);
      } else {
        _display = '0';
      }
    });
  }

  // Handle decimal button press
  void _onDecimalPressed() {
    setState(() {
      if (!_display.contains('.')) {
        _display += '.';
      }
    });
  }

  // Handle toggle sign button press
  void _onToggleSignPressed() {
    setState(() {
      if (_display.startsWith('-')) {
        _display = _display.substring(1);
      } else if (_display != '0') {
        _display = '-$_display';
      }
    });
  }

  // Handle function button press
  void _onFunctionPressed(String function) {
    setState(() {
      if (function == 'sqrt') {
        try {
          double value = double.parse(_display);
          if (value < 0) {
            _display = 'Error';
          } else {
            double result = math.sqrt(value);
            if (result == result.toInt()) {
              _display = result.toInt().toString();
            } else {
              _display = result.toString();
            }
          }
        } catch (e) {
          _display = 'Error';
        }
      } else {
        _expression = '$function($_display)';
        _onEqualsPressed();
      }
    });
  }
}
