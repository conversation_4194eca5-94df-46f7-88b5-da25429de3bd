import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:smart_toolbox/models/data_provider.dart';
import 'package:smart_toolbox/models/tool.dart';
import 'package:smart_toolbox/screens/category_tools_screen.dart';
import 'package:smart_toolbox/screens/tool_detail_screen.dart';
import 'package:smart_toolbox/utils/page_transitions.dart';
import 'package:smart_toolbox/widgets/category_card.dart';
import 'package:smart_toolbox/widgets/tool_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _showSearch = false;
  final TextEditingController _searchController = TextEditingController();
  List<Tool> _filteredTools = [];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _toggleSearch() {
    setState(() {
      _showSearch = !_showSearch;
      if (!_showSearch) {
        _searchController.clear();
        _filteredTools.clear();
      }
    });
  }

  void _filterTools(String query, List<Tool> allTools) {
    setState(() {
      if (query.isEmpty) {
        _filteredTools.clear();
      } else {
        _filteredTools = allTools
            .where(
              (tool) =>
                  tool.name.toLowerCase().contains(query.toLowerCase()) ||
                  tool.description.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  tool.category.toLowerCase().contains(query.toLowerCase()),
            )
            .toList();
      }
    });
  }

  // Get icon for category
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'media':
        return Icons.image;
      case 'document':
        return Icons.description;
      case 'developer':
        return Icons.code;
      case 'audio':
        return Icons.audiotrack;
      case 'ai':
        return Icons.psychology;
      case 'productivity':
        return Icons.schedule;
      case 'privacy':
        return Icons.security;
      case 'travel':
        return Icons.travel_explore;
      default:
        return Icons.build;
    }
  }

  // Build section header
  Widget _buildSectionHeader(
    BuildContext context,
    String title, {
    VoidCallback? onSeeAll,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: Theme.of(context).textTheme.titleLarge),
          if (onSeeAll != null)
            TextButton(
              onPressed: onSeeAll,
              child: Row(
                children: [
                  Text(
                    'See All',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.arrow_forward,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<DataProvider>(context);
    final categories = dataProvider.categories;

    // Get premium tools for the featured section
    final premiumTools = dataProvider.tools
        .where((tool) => tool.isPremium)
        .take(4)
        .toList();

    // Get popular tools (non-premium) for the popular section
    final popularTools = dataProvider.tools
        .where((tool) => !tool.isPremium)
        .take(6)
        .toList();

    // Get recently added tools
    final recentTools = dataProvider.tools.reversed.take(4).toList();

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Smart Toolbox',
                        style: Theme.of(context).textTheme.displayMedium,
                      ),
                      Text(
                        'All the tools you need in one place',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                  GestureDetector(
                    onTap: _toggleSearch,
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: _showSearch
                            ? Theme.of(context).colorScheme.secondary
                            : Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(30),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(
                              context,
                            ).colorScheme.primary.withAlpha(50),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Icon(
                        _showSearch ? Icons.close : Icons.search,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),

              // Search Box
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: _showSearch ? 60 : 0,
                margin: EdgeInsets.only(top: _showSearch ? 16 : 0),
                child: _showSearch
                    ? TextField(
                        controller: _searchController,
                        onChanged: (query) =>
                            _filterTools(query, dataProvider.tools),
                        autofocus: true,
                        decoration: InputDecoration(
                          hintText: 'Search tools...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: _searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    _searchController.clear();
                                    _filterTools('', dataProvider.tools);
                                  },
                                )
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          filled: true,
                          fillColor: Theme.of(context).colorScheme.surface,
                        ),
                      )
                    : const SizedBox.shrink(),
              ),

              // Search Results
              if (_showSearch && _filteredTools.isNotEmpty)
                Container(
                  margin: const EdgeInsets.only(top: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Search Results',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: _filteredTools.length > 5
                            ? 5
                            : _filteredTools.length,
                        separatorBuilder: (context, index) => const Divider(),
                        itemBuilder: (context, index) {
                          final tool = _filteredTools[index];
                          return ListTile(
                            leading: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Theme.of(
                                  context,
                                ).colorScheme.primary.withAlpha(20),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Icon(
                                _getCategoryIcon(tool.category),
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            title: Text(tool.name),
                            subtitle: Text(
                              tool.description,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            trailing: tool.isPremium
                                ? const Icon(Icons.star, color: Colors.amber)
                                : null,
                            onTap: () {
                              setState(() {
                                _showSearch = false;
                                _searchController.clear();
                                _filteredTools.clear();
                              });
                              Navigator.push(
                                context,
                                PageTransitions.scaleTransition(
                                  page: ToolDetailScreen(tool: tool),
                                ),
                              );
                            },
                          );
                        },
                      ),
                      if (_filteredTools.length > 5)
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Center(
                            child: TextButton(
                              onPressed: () {
                                // Show all results
                                showModalBottomSheet(
                                  context: context,
                                  isScrollControlled: true,
                                  backgroundColor: Colors.transparent,
                                  builder: (context) => DraggableScrollableSheet(
                                    initialChildSize: 0.9,
                                    minChildSize: 0.5,
                                    maxChildSize: 0.9,
                                    builder: (_, scrollController) => Container(
                                      decoration: BoxDecoration(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.surface,
                                        borderRadius:
                                            const BorderRadius.vertical(
                                              top: Radius.circular(20),
                                            ),
                                      ),
                                      padding: const EdgeInsets.all(16),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Center(
                                            child: Container(
                                              width: 40,
                                              height: 4,
                                              decoration: BoxDecoration(
                                                color: Colors.grey.withAlpha(
                                                  150,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(2),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Text(
                                            'All Search Results',
                                            style: Theme.of(
                                              context,
                                            ).textTheme.titleLarge,
                                          ),
                                          const SizedBox(height: 16),
                                          Expanded(
                                            child: ListView.separated(
                                              controller: scrollController,
                                              itemCount: _filteredTools.length,
                                              separatorBuilder:
                                                  (context, index) =>
                                                      const Divider(),
                                              itemBuilder: (context, index) {
                                                final tool =
                                                    _filteredTools[index];
                                                return ListTile(
                                                  leading: Container(
                                                    width: 40,
                                                    height: 40,
                                                    decoration: BoxDecoration(
                                                      color: Theme.of(context)
                                                          .colorScheme
                                                          .primary
                                                          .withAlpha(20),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            10,
                                                          ),
                                                    ),
                                                    child: Icon(
                                                      _getCategoryIcon(
                                                        tool.category,
                                                      ),
                                                      color: Theme.of(
                                                        context,
                                                      ).colorScheme.primary,
                                                    ),
                                                  ),
                                                  title: Text(tool.name),
                                                  subtitle: Text(
                                                    tool.description,
                                                    maxLines: 1,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                  trailing: tool.isPremium
                                                      ? const Icon(
                                                          Icons.star,
                                                          color: Colors.amber,
                                                        )
                                                      : null,
                                                  onTap: () {
                                                    Navigator.pop(context);
                                                    setState(() {
                                                      _showSearch = false;
                                                      _searchController.clear();
                                                      _filteredTools.clear();
                                                    });
                                                    Navigator.push(
                                                      context,
                                                      PageTransitions.scaleTransition(
                                                        page: ToolDetailScreen(
                                                          tool: tool,
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                              child: Text(
                                'View All ${_filteredTools.length} Results',
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

              const SizedBox(height: 24),

              // Premium Tools
              _buildSectionHeader(
                context,
                'Premium Tools',
                onSeeAll: () {
                  // Navigate to premium tools screen
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Upgrade to access all premium tools!'),
                      backgroundColor: Colors.amber,
                    ),
                  );
                },
              ),
              SizedBox(
                height: 200,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: premiumTools.length,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 180,
                      margin: const EdgeInsets.only(right: 16),
                      child: Card(
                        elevation: 4,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              PageTransitions.materialTransition(
                                page: ToolDetailScreen(
                                  tool: premiumTools[index],
                                ),
                                context: context,
                                originOffset: Offset(
                                  MediaQuery.of(context).size.width / 2,
                                  MediaQuery.of(context).size.height / 3,
                                ),
                              ),
                            );
                          },
                          borderRadius: BorderRadius.circular(16),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.amber,
                                        borderRadius: BorderRadius.circular(8),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.amber.withAlpha(100),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: const Text(
                                        'PRO',
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 10,
                                          letterSpacing: 0.5,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                Container(
                                  width: 40,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.primary.withAlpha(25),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Icon(
                                    _getCategoryIcon(
                                      premiumTools[index].category,
                                    ),
                                    color: Theme.of(
                                      context,
                                    ).colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(height: 12),
                                Text(
                                  premiumTools[index].name,
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(fontWeight: FontWeight.bold),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  premiumTools[index].description,
                                  style: Theme.of(context).textTheme.bodySmall,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(height: 24),

              // Categories
              _buildSectionHeader(context, 'Categories'),
              SizedBox(
                height: 180,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 160,
                      margin: const EdgeInsets.only(right: 16),
                      child: CategoryCard(
                        category: categories[index],
                        onTap: () {
                          Navigator.push(
                            context,
                            PageTransitions.slideRightTransition(
                              page: CategoryToolsScreen(
                                category: categories[index],
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(height: 24),

              // Recent Tools
              _buildSectionHeader(context, 'Recent Tools'),
              MasonryGridView.count(
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: recentTools.length,
                itemBuilder: (context, index) {
                  return ToolCard(
                    tool: recentTools[index],
                    onTap: () {
                      Navigator.push(
                        context,
                        PageTransitions.scaleTransition(
                          page: ToolDetailScreen(tool: recentTools[index]),
                        ),
                      );
                    },
                  );
                },
              ),

              const SizedBox(height: 24),

              // Popular Tools
              _buildSectionHeader(context, 'Popular Tools'),
              MasonryGridView.count(
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: popularTools.length,
                itemBuilder: (context, index) {
                  return ToolCard(
                    tool: popularTools[index],
                    onTap: () {
                      Navigator.push(
                        context,
                        PageTransitions.scaleTransition(
                          page: ToolDetailScreen(tool: popularTools[index]),
                        ),
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
