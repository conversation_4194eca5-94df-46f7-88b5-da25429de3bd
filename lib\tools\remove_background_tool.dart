import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class RemoveBackgroundTool extends StatefulWidget {
  const RemoveBackgroundTool({super.key});

  @override
  State<RemoveBackgroundTool> createState() => _RemoveBackgroundToolState();
}

class _RemoveBackgroundToolState extends State<RemoveBackgroundTool> {
  File? _selectedImage;
  File? _processedImage;
  bool _isProcessing = false;
  String? _errorMessage;
  double _processingProgress = 0.0;

  final ImagePicker _picker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Remove Image Background',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Upload an image and remove its background using AI technology.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 24),

            // Image selection
            if (_selectedImage == null)
              _buildImageSelector(theme)
            else
              _buildImagePreview(theme),

            const SizedBox(height: 24),

            // Processing button
            if (_selectedImage != null && !_isProcessing)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _removeBackground,
                  icon: const Icon(Icons.auto_fix_high),
                  label: const Text('Remove Background'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),

            // Processing indicator
            if (_isProcessing) _buildProcessingIndicator(theme),

            // Result
            if (_processedImage != null) _buildResult(theme),

            // Error message
            if (_errorMessage != null) _buildErrorMessage(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSelector(ThemeData theme) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        border: Border.all(
          color: theme.colorScheme.outline,
          style: BorderStyle.solid,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: _pickImage,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_upload_outlined,
              size: 48,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Tap to select an image',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Supports JPG, PNG, WebP formats',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagePreview(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Selected Image',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          constraints: const BoxConstraints(maxHeight: 300),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: theme.colorScheme.outline),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(_selectedImage!, fit: BoxFit.contain),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            TextButton.icon(
              onPressed: _pickImage,
              icon: const Icon(Icons.swap_horiz),
              label: const Text('Change Image'),
            ),
            const Spacer(),
            TextButton.icon(
              onPressed: () {
                setState(() {
                  _selectedImage = null;
                  _processedImage = null;
                  _errorMessage = null;
                });
              },
              icon: const Icon(Icons.clear),
              label: const Text('Clear'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProcessingIndicator(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          CircularProgressIndicator(
            value: _processingProgress > 0 ? _processingProgress : null,
          ),
          const SizedBox(height: 16),
          Text('Removing background...', style: theme.textTheme.titleMedium),
          const SizedBox(height: 8),
          Text(
            'This may take a few moments',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onPrimaryContainer.withValues(
                alpha: 0.7,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResult(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(
          'Result',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          constraints: const BoxConstraints(maxHeight: 300),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: theme.colorScheme.outline),
            // Checkered background to show transparency
            color: Colors.grey[100],
          ),
          child: Stack(
            children: [
              // Transparency pattern
              Positioned.fill(
                child: CustomPaint(painter: TransparencyPatternPainter()),
              ),
              // Image
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.file(_processedImage!, fit: BoxFit.contain),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _saveImage,
                icon: const Icon(Icons.download),
                label: const Text('Save'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _shareImage,
                icon: const Icon(Icons.share),
                label: const Text('Share'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildErrorMessage(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: Colors.red, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.red),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.red, size: 20),
            onPressed: () {
              setState(() {
                _errorMessage = null;
              });
            },
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 90,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _processedImage = null;
          _errorMessage = null;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error selecting image: $e';
      });
    }
  }

  Future<void> _removeBackground() async {
    if (_selectedImage == null) return;

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
      _processingProgress = 0.0;
    });

    try {
      // Simulate AI background removal process
      // In a real implementation, you would use a service like:
      // - Remove.bg API
      // - Adobe Creative SDK
      // - Local ML model

      // For demo purposes, we'll simulate the process
      await _simulateBackgroundRemoval();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error removing background: $e';
        _isProcessing = false;
      });
    }
  }

  Future<void> _simulateBackgroundRemoval() async {
    // Simulate processing steps
    for (int i = 0; i <= 100; i += 10) {
      await Future.delayed(const Duration(milliseconds: 200));
      if (mounted) {
        setState(() {
          _processingProgress = i / 100;
        });
      }
    }

    // Create a processed version (for demo, just copy the original)
    final tempDir = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final outputPath = path.join(tempDir.path, 'bg_removed_$timestamp.png');

    // In a real implementation, this would be the processed image
    final processedFile = await _selectedImage!.copy(outputPath);

    if (mounted) {
      setState(() {
        _processedImage = processedFile;
        _isProcessing = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Background removed successfully!'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _saveImage() async {
    if (_processedImage == null) return;

    try {
      // Request storage permission
      var status = await Permission.storage.request();
      if (!status.isGranted) {
        throw Exception('Storage permission denied');
      }

      // Save to downloads directory
      final downloadsDir = await getExternalStorageDirectory();
      if (downloadsDir == null) {
        throw Exception('Could not access storage directory');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'bg_removed_$timestamp.png';
      final destinationPath = path.join(downloadsDir.path, fileName);

      await _processedImage!.copy(destinationPath);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Image saved to: $fileName'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error saving image: $e';
      });
    }
  }

  Future<void> _shareImage() async {
    if (_processedImage == null) return;

    try {
      await Share.shareXFiles([
        XFile(_processedImage!.path),
      ], text: 'Background removed image from Smart Toolbox');
    } catch (e) {
      setState(() {
        _errorMessage = 'Error sharing image: $e';
      });
    }
  }
}

/// Custom painter for transparency pattern background
class TransparencyPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    const double squareSize = 10.0;
    final paint = Paint();

    for (double x = 0; x < size.width; x += squareSize) {
      for (double y = 0; y < size.height; y += squareSize) {
        final isEven =
            ((x / squareSize).floor() + (y / squareSize).floor()) % 2 == 0;
        paint.color = isEven ? Colors.white : Colors.grey[300]!;
        canvas.drawRect(Rect.fromLTWH(x, y, squareSize, squareSize), paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
