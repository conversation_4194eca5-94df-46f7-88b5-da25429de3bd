import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:smart_toolbox/models/tool.dart';
import 'package:smart_toolbox/screens/batch_process_screen.dart';
import 'package:smart_toolbox/utils/page_transitions.dart';

class FilePickerUtil {
  /// Pick multiple files for batch processing
  static Future<void> pickFilesForBatchProcessing(
    BuildContext context,
    Tool tool,
    List<String> allowedExtensions,
  ) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final filePaths = result.files
            .where((file) => file.path != null)
            .map((file) => file.path!)
            .toList();

        if (filePaths.isNotEmpty) {
          if (context.mounted) {
            Navigator.push(
              context,
              PageTransitions.slideRightTransition(
                page: BatchProcessScreen(
                  tool: tool,
                  filePaths: filePaths,
                ),
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking files: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Pick a single file
  static Future<String?> pickSingleFile(
    BuildContext context,
    List<String> allowedExtensions,
  ) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty && result.files.first.path != null) {
        return result.files.first.path!;
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
    return null;
  }

  /// Pick a directory
  static Future<String?> pickDirectory(BuildContext context) async {
    try {
      final result = await FilePicker.platform.getDirectoryPath();
      return result;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking directory: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
    return null;
  }
  
  /// Get file extension from path
  static String getFileExtension(String path) {
    return path.split('.').last.toLowerCase();
  }
  
  /// Get file name from path
  static String getFileName(String path) {
    return path.split('/').last;
  }
  
  /// Get file size in human-readable format
  static String formatFileSize(int size) {
    if (size < 1024) {
      return '$size B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
