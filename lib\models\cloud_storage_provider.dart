import 'package:flutter/foundation.dart';

enum CloudStorageType {
  googleDrive,
  dropbox,
  oneDrive,
  local,
}

enum CloudStorageStatus {
  disconnected,
  connecting,
  connected,
  error,
}

class CloudStorageAccount {
  final String id;
  final String name;
  final CloudStorageType type;
  final String? email;
  final String? avatarUrl;
  final CloudStorageStatus status;
  final String? errorMessage;
  final int usedSpace;
  final int totalSpace;
  
  CloudStorageAccount({
    required this.id,
    required this.name,
    required this.type,
    this.email,
    this.avatarUrl,
    this.status = CloudStorageStatus.disconnected,
    this.errorMessage,
    this.usedSpace = 0,
    this.totalSpace = 0,
  });
  
  double get usagePercentage => 
      totalSpace > 0 ? (usedSpace / totalSpace) * 100 : 0;
  
  String get formattedUsedSpace => _formatSize(usedSpace);
  
  String get formattedTotalSpace => _formatSize(totalSpace);
  
  String get formattedUsage => 
      '$formattedUsedSpace of $formattedTotalSpace (${usagePercentage.toStringAsFixed(1)}%)';
  
  String _formatSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
  
  CloudStorageAccount copyWith({
    String? id,
    String? name,
    CloudStorageType? type,
    String? email,
    String? avatarUrl,
    CloudStorageStatus? status,
    String? errorMessage,
    int? usedSpace,
    int? totalSpace,
  }) {
    return CloudStorageAccount(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      usedSpace: usedSpace ?? this.usedSpace,
      totalSpace: totalSpace ?? this.totalSpace,
    );
  }
}

class CloudFile {
  final String id;
  final String name;
  final String path;
  final String? mimeType;
  final int size;
  final DateTime lastModified;
  final bool isDirectory;
  final CloudStorageType storageType;
  
  CloudFile({
    required this.id,
    required this.name,
    required this.path,
    this.mimeType,
    required this.size,
    required this.lastModified,
    required this.isDirectory,
    required this.storageType,
  });
  
  String get formattedSize => _formatSize(size);
  
  String get formattedLastModified {
    final now = DateTime.now();
    final difference = now.difference(lastModified);
    
    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} years ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }
  
  String _formatSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}

class CloudStorageProvider with ChangeNotifier {
  final List<CloudStorageAccount> _accounts = [];
  final List<CloudFile> _files = [];
  String _currentPath = '/';
  CloudStorageAccount? _selectedAccount;
  
  // Getters
  List<CloudStorageAccount> get accounts => [..._accounts];
  List<CloudFile> get files => [..._files];
  String get currentPath => _currentPath;
  CloudStorageAccount? get selectedAccount => _selectedAccount;
  bool get isConnected => _selectedAccount?.status == CloudStorageStatus.connected;
  
  // Constructor with demo data
  CloudStorageProvider() {
    _initDemoData();
  }
  
  // Initialize demo data
  void _initDemoData() {
    // Add demo accounts
    _accounts.add(
      CloudStorageAccount(
        id: 'google_drive_1',
        name: 'Google Drive',
        type: CloudStorageType.googleDrive,
        email: '<EMAIL>',
        status: CloudStorageStatus.connected,
        usedSpace: 5 * 1024 * 1024 * 1024, // 5 GB
        totalSpace: 15 * 1024 * 1024 * 1024, // 15 GB
      ),
    );
    
    _accounts.add(
      CloudStorageAccount(
        id: 'dropbox_1',
        name: 'Dropbox',
        type: CloudStorageType.dropbox,
        email: '<EMAIL>',
        status: CloudStorageStatus.disconnected,
        usedSpace: 2 * 1024 * 1024 * 1024, // 2 GB
        totalSpace: 5 * 1024 * 1024 * 1024, // 5 GB
      ),
    );
    
    _accounts.add(
      CloudStorageAccount(
        id: 'onedrive_1',
        name: 'OneDrive',
        type: CloudStorageType.oneDrive,
        email: '<EMAIL>',
        status: CloudStorageStatus.disconnected,
        usedSpace: 3 * 1024 * 1024 * 1024, // 3 GB
        totalSpace: 10 * 1024 * 1024 * 1024, // 10 GB
      ),
    );
    
    // Set selected account
    _selectedAccount = _accounts.first;
    
    // Add demo files
    _loadDemoFiles();
  }
  
  // Load demo files
  void _loadDemoFiles() {
    _files.clear();
    
    // Add demo directories
    _files.add(
      CloudFile(
        id: 'dir_1',
        name: 'Documents',
        path: '/Documents',
        size: 0,
        lastModified: DateTime.now().subtract(const Duration(days: 5)),
        isDirectory: true,
        storageType: _selectedAccount?.type ?? CloudStorageType.local,
      ),
    );
    
    _files.add(
      CloudFile(
        id: 'dir_2',
        name: 'Photos',
        path: '/Photos',
        size: 0,
        lastModified: DateTime.now().subtract(const Duration(days: 2)),
        isDirectory: true,
        storageType: _selectedAccount?.type ?? CloudStorageType.local,
      ),
    );
    
    // Add demo files
    _files.add(
      CloudFile(
        id: 'file_1',
        name: 'Report.pdf',
        path: '/Report.pdf',
        mimeType: 'application/pdf',
        size: 2 * 1024 * 1024, // 2 MB
        lastModified: DateTime.now().subtract(const Duration(hours: 5)),
        isDirectory: false,
        storageType: _selectedAccount?.type ?? CloudStorageType.local,
      ),
    );
    
    _files.add(
      CloudFile(
        id: 'file_2',
        name: 'Presentation.pptx',
        path: '/Presentation.pptx',
        mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        size: 5 * 1024 * 1024, // 5 MB
        lastModified: DateTime.now().subtract(const Duration(days: 1)),
        isDirectory: false,
        storageType: _selectedAccount?.type ?? CloudStorageType.local,
      ),
    );
    
    _files.add(
      CloudFile(
        id: 'file_3',
        name: 'Budget.xlsx',
        path: '/Budget.xlsx',
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        size: 1 * 1024 * 1024, // 1 MB
        lastModified: DateTime.now().subtract(const Duration(minutes: 30)),
        isDirectory: false,
        storageType: _selectedAccount?.type ?? CloudStorageType.local,
      ),
    );
    
    notifyListeners();
  }
  
  // Select account
  void selectAccount(String id) {
    final account = _accounts.firstWhere(
      (account) => account.id == id,
      orElse: () => _accounts.first,
    );
    
    _selectedAccount = account;
    _currentPath = '/';
    _loadDemoFiles();
    
    notifyListeners();
  }
  
  // Connect account
  Future<void> connectAccount(String id) async {
    final index = _accounts.indexWhere((account) => account.id == id);
    if (index < 0) return;
    
    // Update status to connecting
    _accounts[index] = _accounts[index].copyWith(
      status: CloudStorageStatus.connecting,
    );
    notifyListeners();
    
    // Simulate connection delay
    await Future.delayed(const Duration(seconds: 2));
    
    // Update status to connected
    _accounts[index] = _accounts[index].copyWith(
      status: CloudStorageStatus.connected,
    );
    
    // Select the connected account
    _selectedAccount = _accounts[index];
    _currentPath = '/';
    _loadDemoFiles();
    
    notifyListeners();
  }
  
  // Disconnect account
  void disconnectAccount(String id) {
    final index = _accounts.indexWhere((account) => account.id == id);
    if (index < 0) return;
    
    // Update status to disconnected
    _accounts[index] = _accounts[index].copyWith(
      status: CloudStorageStatus.disconnected,
    );
    
    // If this was the selected account, select another connected account
    if (_selectedAccount?.id == id) {
      final connectedAccount = _accounts.firstWhere(
        (account) => account.status == CloudStorageStatus.connected,
        orElse: () => _accounts.first,
      );
      
      _selectedAccount = connectedAccount;
      _currentPath = '/';
      _loadDemoFiles();
    }
    
    notifyListeners();
  }
  
  // Navigate to directory
  void navigateToDirectory(String path) {
    _currentPath = path;
    _loadDemoFiles(); // In a real app, we would load files from the new path
    notifyListeners();
  }
  
  // Navigate up one level
  void navigateUp() {
    if (_currentPath == '/') return;
    
    final parts = _currentPath.split('/');
    parts.removeLast();
    _currentPath = parts.join('/');
    if (_currentPath.isEmpty) _currentPath = '/';
    
    _loadDemoFiles(); // In a real app, we would load files from the new path
    notifyListeners();
  }
}
