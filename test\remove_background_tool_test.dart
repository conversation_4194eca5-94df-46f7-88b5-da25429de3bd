import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_toolbox/tools/remove_background_tool.dart';

void main() {
  group('RemoveBackgroundTool Tests', () {
    testWidgets('should show image selector initially', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: RemoveBackgroundTool())),
      );

      // Wait for initial build
      await tester.pump();

      // Should show the image selector
      expect(find.text('Remove Image Background'), findsOneWidget);
      expect(
        find.text(
          'Upload an image and remove its background using AI technology.',
        ),
        findsOneWidget,
      );
      expect(find.text('Tap to select an image'), findsOneWidget);
      expect(find.text('Supports JPG, PNG, WebP formats'), findsOneWidget);
      expect(find.byIcon(Icons.cloud_upload_outlined), findsOneWidget);
    });

    testWidgets('should show transparency pattern painter', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomPaint(
              painter: TransparencyPatternPainter(),
              size: const Size(100, 100),
            ),
          ),
        ),
      );

      await tester.pump();

      // Should render without errors
      expect(find.byType(CustomPaint), findsAtLeastNWidgets(1));
    });

    testWidgets('should have proper widget structure', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: RemoveBackgroundTool())),
      );

      await tester.pump();

      // Should have main components
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      expect(find.byType(Column), findsAtLeastNWidgets(1));
      expect(find.byType(Container), findsAtLeastNWidgets(1));
    });
  });
}
