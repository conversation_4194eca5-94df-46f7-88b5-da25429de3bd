class Tool {
  final String id;
  final String name;
  final String description;
  final String iconPath;
  final String category;
  final bool isPremium;
  bool isFavorite;

  Tool({
    required this.id,
    required this.name,
    required this.description,
    required this.iconPath,
    required this.category,
    this.isPremium = false,
    this.isFavorite = false,
  });

  Tool copyWith({
    String? id,
    String? name,
    String? description,
    String? iconPath,
    String? category,
    bool? isPremium,
    bool? isFavorite,
  }) {
    return Tool(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconPath: iconPath ?? this.iconPath,
      category: category ?? this.category,
      isPremium: isPremium ?? this.isPremium,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}
