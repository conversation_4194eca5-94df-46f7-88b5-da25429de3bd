import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:smart_toolbox/models/data_provider.dart';
import 'package:smart_toolbox/models/history_provider.dart';
import 'package:smart_toolbox/models/tool.dart';
import 'package:smart_toolbox/screens/tool_implementation_screen.dart';
import 'package:smart_toolbox/utils/file_picker_util.dart';
import 'package:smart_toolbox/utils/page_transitions.dart';
import 'package:smart_toolbox/utils/tool_icons.dart';
import 'package:smart_toolbox/utils/tool_implementation.dart';

class ToolDetailScreen extends StatelessWidget {
  final Tool tool;

  const ToolDetailScreen({super.key, required this.tool});

  // Build the tool interface based on the tool category
  Widget _buildToolInterface(BuildContext context, ThemeData theme) {
    switch (tool.category) {
      case 'media':
        return _buildMediaToolInterface(theme);
      case 'document':
        return _buildDocumentToolInterface(theme);
      case 'developer':
        return _buildDeveloperToolInterface(theme);
      case 'audio':
        return _buildAudioToolInterface(theme);
      case 'ai':
        return _buildAIToolInterface(theme);
      case 'productivity':
        return _buildProductivityToolInterface(theme);
      case 'privacy':
        return _buildPrivacyToolInterface(theme);
      case 'travel':
        return _buildTravelToolInterface(theme);
      default:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.construction,
                size: 64,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'Tool Interface Coming Soon',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Text(
                'This is a placeholder for the actual tool interface',
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
    }
  }

  // Media tool interface
  Widget _buildMediaToolInterface(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withAlpha(20),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withAlpha(50),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.photo_library, color: theme.colorScheme.primary),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Select images to process',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(Icons.add_circle, color: theme.colorScheme.primary),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image,
                    size: 64,
                    color: theme.colorScheme.primary.withAlpha(100),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Drag and drop images here',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text('or click to browse', style: theme.textTheme.bodyMedium),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Document tool interface
  Widget _buildDocumentToolInterface(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.secondary.withAlpha(20),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.secondary.withAlpha(50),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.description, color: theme.colorScheme.secondary),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Select document to process',
                    style: TextStyle(
                      color: theme.colorScheme.secondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(Icons.add_circle, color: theme.colorScheme.secondary),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.file_present,
                    size: 64,
                    color: theme.colorScheme.secondary.withAlpha(100),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Drag and drop documents here',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text('or click to browse', style: theme.textTheme.bodyMedium),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Default placeholder for other tool categories
  Widget _buildDeveloperToolInterface(ThemeData theme) {
    return _buildPlaceholderInterface(theme, Icons.code, 'developer');
  }

  Widget _buildAudioToolInterface(ThemeData theme) {
    return _buildPlaceholderInterface(theme, Icons.audiotrack, 'audio');
  }

  Widget _buildAIToolInterface(ThemeData theme) {
    return _buildPlaceholderInterface(theme, Icons.psychology, 'AI');
  }

  Widget _buildProductivityToolInterface(ThemeData theme) {
    return _buildPlaceholderInterface(theme, Icons.schedule, 'productivity');
  }

  Widget _buildPrivacyToolInterface(ThemeData theme) {
    return _buildPlaceholderInterface(theme, Icons.security, 'privacy');
  }

  Widget _buildTravelToolInterface(ThemeData theme) {
    return _buildPlaceholderInterface(theme, Icons.travel_explore, 'travel');
  }

  // Method to handle starting the tool
  void _startUsingTool(BuildContext context) {
    // Add to history
    final historyProvider = Provider.of<HistoryProvider>(
      context,
      listen: false,
    );
    historyProvider.addToolToHistory(tool);

    // Check if this tool has an implementation
    final implementation = ToolImplementationFactory.getImplementation(tool.id);

    if (implementation != null) {
      // Show the tool interface in a new screen
      Navigator.push(
        context,
        PageTransitions.slideRightTransition(
          page: ToolImplementationScreen(
            tool: tool,
            implementation: implementation,
          ),
        ),
      );
      return;
    }

    // Check if this is a media tool that supports batch processing
    if (tool.category == 'media') {
      _handleMediaTool(context);
      return;
    }

    // Default behavior for other tools
    final snackBarText = tool.isPremium
        ? 'Premium ${tool.name} functionality coming soon!'
        : '${tool.name} functionality coming soon!';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(snackBarText),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        action: SnackBarAction(label: 'OK', onPressed: () {}),
      ),
    );
  }

  // Handle media tools with batch processing
  void _handleMediaTool(BuildContext context) {
    // Show options dialog
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(150),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Choose an option',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            ListTile(
              leading: Icon(
                Icons.file_open,
                color: Theme.of(context).colorScheme.primary,
              ),
              title: const Text('Process a single file'),
              onTap: () {
                Navigator.pop(context);
                _processSingleFile(context);
              },
            ),
            ListTile(
              leading: Icon(
                Icons.folder_open,
                color: Theme.of(context).colorScheme.primary,
              ),
              title: const Text('Batch process multiple files'),
              onTap: () {
                Navigator.pop(context);
                _processBatchFiles(context);
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  // Process a single file
  void _processSingleFile(BuildContext context) async {
    List<String> allowedExtensions = _getAllowedExtensions();

    final filePath = await FilePickerUtil.pickSingleFile(
      context,
      allowedExtensions,
    );

    if (filePath != null && context.mounted) {
      // For now, just show a snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Selected file: ${FilePickerUtil.getFileName(filePath)}',
          ),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // Process multiple files in batch
  void _processBatchFiles(BuildContext context) {
    List<String> allowedExtensions = _getAllowedExtensions();

    FilePickerUtil.pickFilesForBatchProcessing(
      context,
      tool,
      allowedExtensions,
    );
  }

  // Get allowed file extensions based on tool
  List<String> _getAllowedExtensions() {
    switch (tool.id) {
      case 'image_converter':
        return ['jpg', 'jpeg', 'png', 'gif', 'webp'];
      case 'video_converter':
        return ['mp4', 'mov', 'avi', 'mkv'];
      case 'audio_converter':
        return ['mp3', 'wav', 'ogg', 'aac'];
      case 'document_converter':
        return ['pdf', 'doc', 'docx', 'txt'];
      default:
        return ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'txt'];
    }
  }

  // Generic placeholder interface
  Widget _buildPlaceholderInterface(
    ThemeData theme,
    IconData icon,
    String category,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: theme.colorScheme.primary.withAlpha(100)),
          const SizedBox(height: 16),
          Text(
            'This $category tool is coming soon',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'We\'re working hard to bring you the best experience',
            style: theme.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(tool.name),
        actions: [
          IconButton(
            icon: Icon(
              tool.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: tool.isFavorite ? Colors.red : null,
            ),
            onPressed: () {
              Provider.of<DataProvider>(
                context,
                listen: false,
              ).toggleFavorite(tool.id);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tool Header
            Row(
              children: [
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withAlpha(
                      25,
                    ), // 0.1 opacity = 25 alpha (255 * 0.1)
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Image.asset(
                    tool.iconPath,
                    width: 32,
                    height: 32,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        ToolIcons.getIconById(tool.id),
                        color: theme.colorScheme.primary,
                        size: 32,
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(tool.name, style: theme.textTheme.headlineSmall),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          if (tool.isPremium)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 3,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.amber,
                                borderRadius: BorderRadius.circular(8),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.amber.withAlpha(100),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Text(
                                'PRO',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                          if (tool.isPremium) const SizedBox(width: 10),
                          Flexible(
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 3,
                              ),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary.withAlpha(30),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                tool.category.toUpperCase(),
                                style: TextStyle(
                                  color: theme.colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Description
            Text('Description', style: theme.textTheme.titleLarge),
            const SizedBox(height: 8),
            Text(tool.description, style: theme.textTheme.bodyLarge),

            const SizedBox(height: 24),

            // Tool Interface
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Tool Interface', style: theme.textTheme.titleLarge),
                if (tool.isPremium)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.amber.withAlpha(50),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.amber),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.star, color: Colors.amber, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          'Premium Feature',
                          style: TextStyle(
                            color: Colors.amber.shade800,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              height: 300,
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.outline.withAlpha(128),
                ),
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.shadow.withAlpha(20),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: _buildToolInterface(context, theme),
              ),
            ),

            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                if (tool.isPremium)
                  Expanded(
                    flex: 1,
                    child: OutlinedButton.icon(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Upgrade to Pro to unlock all premium features!',
                            ),
                            backgroundColor: Colors.amber,
                          ),
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: const BorderSide(color: Colors.amber),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      icon: const Icon(Icons.star, color: Colors.amber),
                      label: const Text(
                        'Upgrade',
                        style: TextStyle(
                          color: Colors.amber,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                if (tool.isPremium) const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton.icon(
                    onPressed: () {
                      _startUsingTool(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 2,
                    ),
                    icon: const Icon(Icons.play_arrow),
                    label: Text(
                      'Start Using Tool',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
