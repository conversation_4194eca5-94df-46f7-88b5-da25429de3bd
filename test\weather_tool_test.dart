import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_toolbox/tools/weather_tool.dart';

void main() {
  group('WeatherTool Tests', () {
    testWidgets('should show search interface components', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: WeatherTool())),
      );

      // Wait for initial build
      await tester.pump();

      // Should show the search input field and button
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('Enter city name'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should show loading indicator when searching', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        const MaterialApp(home: Scaffold(body: WeatherTool())),
      );

      await tester.pump();

      // Enter a city name
      await tester.enterText(find.byType(TextField), 'London');

      // Tap the search button
      await tester.tap(find.byType(ElevatedButton));

      // Should show loading indicator briefly
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });
  });
}
