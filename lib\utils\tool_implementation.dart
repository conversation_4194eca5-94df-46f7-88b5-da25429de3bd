import 'package:flutter/material.dart';
import 'package:smart_toolbox/tools/calculator_tool.dart';
import 'package:smart_toolbox/tools/image_converter_tool.dart';
import 'package:smart_toolbox/tools/json_formatter_tool.dart';
import 'package:smart_toolbox/tools/qr_code_tool.dart';
import 'package:smart_toolbox/tools/remove_background_tool.dart';
import 'package:smart_toolbox/tools/text_note_tool.dart';
import 'package:smart_toolbox/tools/unit_converter_tool.dart';
import 'package:smart_toolbox/tools/weather_tool.dart';

/// Base interface for all tool implementations
abstract class ToolImplementation {
  /// Execute the tool with the given context
  Future<void> execute(BuildContext context);

  /// Build the tool interface
  Widget buildInterface(BuildContext context);
}

/// Factory for creating tool implementations
class ToolImplementationFactory {
  static ToolImplementation? getImplementation(String toolId) {
    switch (toolId) {
      case 'convert_image': // This matches the ID in data_provider.dart
        return ImageConverterToolImplementation();
      case 'remove_bg': // This matches the ID in data_provider.dart
        return RemoveBackgroundToolImplementation();
      case 'calculator': // This matches the ID in data_provider.dart
        return CalculatorToolImplementation();
      case 'json_formatter': // This matches the ID in data_provider.dart
        return JsonFormatterToolImplementation();
      case 'qr_code': // This matches the ID in data_provider.dart
        return QrCodeToolImplementation();
      case 'unit_converter': // This matches the ID in data_provider.dart
        return UnitConverterToolImplementation();
      case 'text_note': // This matches the ID in data_provider.dart
        return TextNoteToolImplementation();
      case 'weather': // This matches the ID in data_provider.dart
        return WeatherToolImplementation();
      default:
        return null;
    }
  }
}

/// Image Converter Tool Implementation
class ImageConverterToolImplementation implements ToolImplementation {
  @override
  Future<void> execute(BuildContext context) async {
    // Image converter doesn't need execution, it's interactive
    return;
  }

  @override
  Widget buildInterface(BuildContext context) {
    return const ImageConverterTool();
  }
}

/// Calculator Tool Implementation
class CalculatorToolImplementation implements ToolImplementation {
  @override
  Future<void> execute(BuildContext context) async {
    // Calculator doesn't need execution, it's interactive
    return;
  }

  @override
  Widget buildInterface(BuildContext context) {
    return const CalculatorTool();
  }
}

/// JSON Formatter Tool Implementation
class JsonFormatterToolImplementation implements ToolImplementation {
  @override
  Future<void> execute(BuildContext context) async {
    // JSON formatter doesn't need execution, it's interactive
    return;
  }

  @override
  Widget buildInterface(BuildContext context) {
    return const JsonFormatterTool();
  }
}

/// QR Code Tool Implementation
class QrCodeToolImplementation implements ToolImplementation {
  @override
  Future<void> execute(BuildContext context) async {
    // QR code tool doesn't need execution, it's interactive
    return;
  }

  @override
  Widget buildInterface(BuildContext context) {
    return const QrCodeTool();
  }
}

/// Unit Converter Tool Implementation
class UnitConverterToolImplementation implements ToolImplementation {
  @override
  Future<void> execute(BuildContext context) async {
    // Unit converter tool doesn't need execution, it's interactive
    return;
  }

  @override
  Widget buildInterface(BuildContext context) {
    return const UnitConverterTool();
  }
}

/// Text Note Tool Implementation
class TextNoteToolImplementation implements ToolImplementation {
  @override
  Future<void> execute(BuildContext context) async {
    // Text note tool doesn't need execution, it's interactive
    return;
  }

  @override
  Widget buildInterface(BuildContext context) {
    return const TextNoteTool();
  }
}

/// Weather Tool Implementation
class WeatherToolImplementation implements ToolImplementation {
  @override
  Future<void> execute(BuildContext context) async {
    // Weather tool doesn't need execution, it's interactive
    return;
  }

  @override
  Widget buildInterface(BuildContext context) {
    return const WeatherTool();
  }
}

/// Remove Background Tool Implementation
class RemoveBackgroundToolImplementation implements ToolImplementation {
  @override
  Future<void> execute(BuildContext context) async {
    // Remove background tool doesn't need execution, it's interactive
    return;
  }

  @override
  Widget buildInterface(BuildContext context) {
    return const RemoveBackgroundTool();
  }
}

/// Image Converter Interface
class ImageConverterInterface extends StatefulWidget {
  const ImageConverterInterface({super.key});

  @override
  State<ImageConverterInterface> createState() =>
      _ImageConverterInterfaceState();
}

class _ImageConverterInterfaceState extends State<ImageConverterInterface> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image,
            size: 64,
            color: Theme.of(context).colorScheme.primary.withAlpha(100),
          ),
          const SizedBox(height: 16),
          Text(
            'Image Converter',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Will be implemented
            },
            icon: const Icon(Icons.add_photo_alternate),
            label: const Text('Select Image'),
          ),
        ],
      ),
    );
  }
}

/// Calculator Interface
class CalculatorInterface extends StatefulWidget {
  const CalculatorInterface({super.key});

  @override
  State<CalculatorInterface> createState() => _CalculatorInterfaceState();
}

class _CalculatorInterfaceState extends State<CalculatorInterface> {
  final String _display = '0';
  final String _expression = '';
  bool _isScientificMode = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Display
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                _expression,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withAlpha(180),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _display,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.end,
              ),
            ],
          ),
        ),

        // Mode toggle
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text('Scientific Mode'),
              const SizedBox(width: 8),
              Switch(
                value: _isScientificMode,
                onChanged: (value) {
                  setState(() {
                    _isScientificMode = value;
                  });
                },
              ),
            ],
          ),
        ),

        // Calculator buttons will be implemented here
        const Expanded(
          child: Center(child: Text('Calculator buttons will appear here')),
        ),
      ],
    );
  }
}

/// JSON Formatter Interface
class JsonFormatterInterface extends StatefulWidget {
  const JsonFormatterInterface({super.key});

  @override
  State<JsonFormatterInterface> createState() => _JsonFormatterInterfaceState();
}

class _JsonFormatterInterfaceState extends State<JsonFormatterInterface> {
  final TextEditingController _inputController = TextEditingController();
  String _formattedJson = '';
  String _errorMessage = '';

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Input
        TextField(
          controller: _inputController,
          maxLines: 8,
          decoration: InputDecoration(
            labelText: 'Enter JSON',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            hintText: '{"example": "Enter your JSON here"}',
          ),
        ),

        const SizedBox(height: 16),

        // Actions
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton.icon(
              onPressed: () {
                // Format JSON
              },
              icon: const Icon(Icons.format_align_left),
              label: const Text('Format'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                // Validate JSON
              },
              icon: const Icon(Icons.check_circle_outline),
              label: const Text('Validate'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                // Clear input
                _inputController.clear();
                setState(() {
                  _formattedJson = '';
                  _errorMessage = '';
                });
              },
              icon: const Icon(Icons.clear),
              label: const Text('Clear'),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Output
        if (_errorMessage.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withAlpha(30),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red),
            ),
            child: Text(_errorMessage, style: TextStyle(color: Colors.red)),
          )
        else if (_formattedJson.isNotEmpty)
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _formattedJson,
                  style: TextStyle(fontFamily: 'monospace'),
                ),
              ),
            ),
          ),
      ],
    );
  }
}
