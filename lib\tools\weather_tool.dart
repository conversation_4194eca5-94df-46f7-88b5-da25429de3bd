import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';

class WeatherTool extends StatefulWidget {
  const WeatherTool({super.key});

  @override
  State<WeatherTool> createState() => _WeatherToolState();
}

class _WeatherToolState extends State<WeatherTool> {
  final TextEditingController _cityController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  Map<String, dynamic>? _weatherData;

  @override
  void initState() {
    super.initState();
    // Get the user's location when the widget is initialized
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _cityController.dispose();
    super.dispose();
  }

  // Request location permission and get current location
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _errorMessage =
              'Location services are disabled. Please enable them in your device settings.';
          _isLoading = false;
        });
        return;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _errorMessage =
                'Location permission denied. Please enable it in app settings.';
            _isLoading = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _errorMessage =
              'Location permission permanently denied. Please enable it in app settings.';
          _isLoading = false;
        });
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.low,
          timeLimit: Duration(seconds: 10),
        ),
      );

      // Get weather data for current location
      await _getWeatherByCoordinates(position.latitude, position.longitude);
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error getting location: $e';
          _isLoading = false;
        });
      }
    }
  }

  // Get weather data using coordinates
  Future<void> _getWeatherByCoordinates(double lat, double lon) async {
    try {
      const apiKey = '15fa36bfdf2f56a4c2fe559c4fbc9c3d';
      final url =
          'https://api.openweathermap.org/data/2.5/weather?lat=$lat&lon=$lon&appid=$apiKey&units=metric';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        try {
          // Map the API response to our weather data format
          final weatherData = {
            'city': data['name'],
            'country': data['sys']['country'],
            'temperature': data['main']['temp'].round(),
            'condition': data['weather'][0]['main'],
            'description': data['weather'][0]['description'],
            'humidity': data['main']['humidity'],
            'windSpeed': (data['wind']['speed'] * 3.6)
                .round(), // Convert m/s to km/h
            'icon': _getWeatherIcon(data['weather'][0]['id']),
          };

          if (mounted) {
            setState(() {
              _weatherData = weatherData;
              _cityController.text = data['name'];
              _isLoading = false;
            });
          }
        } catch (e) {
          if (mounted) {
            setState(() {
              _errorMessage = 'Error parsing weather data: $e';
              _isLoading = false;
            });
          }
        }
      } else {
        if (mounted) {
          setState(() {
            _errorMessage =
                'Error fetching weather data. Please try again later.';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Network error: $e';
          _isLoading = false;
        });
      }
    }
  }

  // Get weather data for a city using OpenWeatherMap API
  Future<void> _getWeather() async {
    final city = _cityController.text.trim();

    if (city.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a city name';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _weatherData = null;
    });

    try {
      // Using OpenWeatherMap API with a free API key
      // Note: In a production app, you would store this key securely
      const apiKey = '15fa36bfdf2f56a4c2fe559c4fbc9c3d';
      final url =
          'https://api.openweathermap.org/data/2.5/weather?q=$city&appid=$apiKey&units=metric';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        try {
          // Map the API response to our weather data format
          final weatherData = {
            'city': data['name'],
            'country': data['sys']['country'],
            'temperature': data['main']['temp'].round(),
            'condition': data['weather'][0]['main'],
            'description': data['weather'][0]['description'],
            'humidity': data['main']['humidity'],
            'windSpeed': (data['wind']['speed'] * 3.6)
                .round(), // Convert m/s to km/h
            'icon': _getWeatherIcon(data['weather'][0]['id']),
          };

          setState(() {
            _weatherData = weatherData;
            _isLoading = false;
          });
        } catch (e) {
          setState(() {
            _errorMessage = 'Error parsing weather data: $e';
            _isLoading = false;
          });
        }
      } else if (response.statusCode == 404) {
        setState(() {
          _errorMessage =
              'City not found. Please check the spelling and try again.';
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage =
              'Error fetching weather data. Please try again later.';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Network error: $e';
        _isLoading = false;
      });
    }
  }

  // Get weather icon based on weather condition ID
  IconData _getWeatherIcon(int conditionId) {
    // Weather condition codes: https://openweathermap.org/weather-conditions
    if (conditionId >= 200 && conditionId < 300) {
      return Icons.thunderstorm; // Thunderstorm
    } else if (conditionId >= 300 && conditionId < 400) {
      return Icons.grain; // Drizzle
    } else if (conditionId >= 500 && conditionId < 600) {
      return Icons.beach_access; // Rain
    } else if (conditionId >= 600 && conditionId < 700) {
      return Icons.ac_unit; // Snow
    } else if (conditionId >= 700 && conditionId < 800) {
      return Icons.filter_drama; // Atmosphere (fog, mist, etc.)
    } else if (conditionId == 800) {
      return Icons.wb_sunny; // Clear
    } else if (conditionId > 800) {
      return Icons.cloud; // Clouds
    } else {
      return Icons.help_outline; // Unknown
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search bar
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _cityController,
                  decoration: InputDecoration(
                    labelText: 'Enter city name',
                    hintText: 'e.g., London, New York, Tokyo',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.search),
                  ),
                  textCapitalization: TextCapitalization.words,
                  onSubmitted: (_) => _getWeather(),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: _isLoading ? null : _getWeather,
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: theme.colorScheme.primary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Text('Search'),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Weather display
          if (_weatherData != null)
            Expanded(
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 400),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // City and country
                      Text(
                        '${_weatherData!['city']}, ${_weatherData!['country']}',
                        style: theme.textTheme.headlineMedium?.copyWith(
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 24),

                      // Weather icon and temperature
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _weatherData!['icon'] as IconData,
                            size: 80,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                          const SizedBox(width: 16),
                          Text(
                            '${_weatherData!['temperature']}°C',
                            style: theme.textTheme.displayMedium?.copyWith(
                              color: theme.colorScheme.onPrimaryContainer,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Weather condition
                      Text(
                        _weatherData!['condition'] as String,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                      ),

                      // Weather description
                      if (_weatherData!.containsKey('description'))
                        Text(
                          _weatherData!['description'] as String,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.onPrimaryContainer,
                            fontStyle: FontStyle.italic,
                          ),
                          textAlign: TextAlign.center,
                        ),

                      const SizedBox(height: 24),

                      // Additional info
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildInfoItem(
                            theme,
                            Icons.water_drop,
                            'Humidity',
                            '${_weatherData!['humidity']}%',
                          ),
                          _buildInfoItem(
                            theme,
                            Icons.air,
                            'Wind',
                            '${_weatherData!['windSpeed']} km/h',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )
          else if (!_isLoading)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.cloud_queue,
                      size: 80,
                      color: theme.colorScheme.primary.withAlpha(128),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Search for a city',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(179),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Error message
          if (_errorMessage != null)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withAlpha(30),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 20),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.red, size: 20),
                    onPressed: () {
                      setState(() {
                        _errorMessage = null;
                      });
                    },
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Dismiss',
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Build info item widget
  Widget _buildInfoItem(
    ThemeData theme,
    IconData icon,
    String label,
    String value,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: theme.colorScheme.onPrimaryContainer.withAlpha(204),
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onPrimaryContainer.withAlpha(204),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            color: theme.colorScheme.onPrimaryContainer,
          ),
        ),
      ],
    );
  }
}
