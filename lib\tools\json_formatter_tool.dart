import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class JsonFormatterTool extends StatefulWidget {
  const JsonFormatterTool({super.key});

  @override
  State<JsonFormatterTool> createState() => _JsonFormatterToolState();
}

class _JsonFormatterToolState extends State<JsonFormatterTool> {
  final TextEditingController _inputController = TextEditingController();
  String _formattedJson = '';
  String _errorMessage = '';
  bool _isValidating = false;
  
  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Input
        TextField(
          controller: _inputController,
          maxLines: 8,
          decoration: InputDecoration(
            labelText: 'Enter JSON',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            hintText: '{"example": "Enter your JSON here"}',
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Actions
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton.icon(
              onPressed: _formatJson,
              icon: const Icon(Icons.format_align_left),
              label: const Text('Format'),
            ),
            ElevatedButton.icon(
              onPressed: _validateJson,
              icon: const Icon(Icons.check_circle_outline),
              label: const Text('Validate'),
            ),
            ElevatedButton.icon(
              onPressed: _clearInput,
              icon: const Icon(Icons.clear),
              label: const Text('Clear'),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Loading indicator
        if (_isValidating)
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          ),
        
        // Error message
        if (_errorMessage.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withAlpha(30),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    _errorMessage,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ),
        
        // Output
        if (_formattedJson.isNotEmpty)
          Expanded(
            child: Stack(
              children: [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      _formattedJson,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: 8,
                  right: 8,
                  child: IconButton(
                    icon: const Icon(Icons.copy),
                    onPressed: () => _copyToClipboard(_formattedJson),
                    tooltip: 'Copy to clipboard',
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
  
  // Format JSON
  void _formatJson() {
    final input = _inputController.text.trim();
    
    if (input.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter some JSON to format';
        _formattedJson = '';
      });
      return;
    }
    
    setState(() {
      _isValidating = true;
      _errorMessage = '';
    });
    
    try {
      // Parse and format JSON
      final dynamic parsedJson = json.decode(input);
      final String formatted = const JsonEncoder.withIndent('  ').convert(parsedJson);
      
      setState(() {
        _formattedJson = formatted;
        _isValidating = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid JSON: ${e.toString()}';
        _formattedJson = '';
        _isValidating = false;
      });
    }
  }
  
  // Validate JSON
  void _validateJson() {
    final input = _inputController.text.trim();
    
    if (input.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter some JSON to validate';
        _formattedJson = '';
      });
      return;
    }
    
    setState(() {
      _isValidating = true;
      _errorMessage = '';
      _formattedJson = '';
    });
    
    try {
      // Parse JSON to validate it
      json.decode(input);
      
      setState(() {
        _errorMessage = '';
        _formattedJson = 'JSON is valid!';
        _isValidating = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Invalid JSON: ${e.toString()}';
        _formattedJson = '';
        _isValidating = false;
      });
    }
  }
  
  // Clear input
  void _clearInput() {
    _inputController.clear();
    setState(() {
      _formattedJson = '';
      _errorMessage = '';
    });
  }
  
  // Copy to clipboard
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Copied to clipboard'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
