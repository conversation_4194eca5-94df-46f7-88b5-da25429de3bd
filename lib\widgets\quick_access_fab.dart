import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:smart_toolbox/models/data_provider.dart';
import 'package:smart_toolbox/models/history_provider.dart';
import 'package:smart_toolbox/models/tool.dart';
import 'package:smart_toolbox/screens/tool_detail_screen.dart';
import 'package:smart_toolbox/utils/page_transitions.dart';
import 'package:smart_toolbox/utils/tool_icons.dart';

class QuickAccessFAB extends StatefulWidget {
  const QuickAccessFAB({super.key});

  @override
  State<QuickAccessFAB> createState() => _QuickAccessFABState();
}

class _QuickAccessFABState extends State<QuickAccessFAB>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isOpen = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _isOpen = !_isOpen;
      if (_isOpen) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final historyProvider = Provider.of<HistoryProvider>(context);
    final dataProvider = Provider.of<DataProvider>(context);

    // Get recent tools
    final recentToolIds = historyProvider.getRecentToolIds(limit: 4);
    final recentTools = recentToolIds
        .map(
          (id) => dataProvider.tools.firstWhere(
            (tool) => tool.id == id,
            orElse: () => dataProvider.tools.first,
          ),
        )
        .toList();

    return Container(
      alignment: Alignment.bottomRight,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Quick access tools
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return ScaleTransition(
                scale: animation,
                child: FadeTransition(opacity: animation, child: child),
              );
            },
            child: _isOpen && recentTools.isNotEmpty
                ? Column(
                    key: const ValueKey('tools_column'),
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      ...recentTools.map(
                        (tool) => _buildMiniToolButton(context, tool),
                      ),
                      const SizedBox(height: 16),
                    ],
                  )
                : const SizedBox.shrink(key: ValueKey('empty')),
          ),

          // Main FAB
          FloatingActionButton(
            onPressed: _toggle,
            backgroundColor: Theme.of(context).colorScheme.primary,
            child: AnimatedIcon(
              icon: AnimatedIcons.menu_close,
              progress: _animationController,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMiniToolButton(BuildContext context, Tool tool) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Tool name label
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withAlpha(230),
              borderRadius: const BorderRadius.horizontal(
                left: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(50),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              tool.name,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ),

          // Tool button
          Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(50),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
              shape: BoxShape.circle,
            ),
            child: FloatingActionButton.small(
              heroTag: 'quick_${tool.id}',
              onPressed: () {
                // Add to history
                Provider.of<HistoryProvider>(
                  context,
                  listen: false,
                ).addToolToHistory(tool);

                // Navigate to tool screen
                Navigator.push(
                  context,
                  PageTransitions.scaleTransition(
                    page: ToolDetailScreen(tool: tool),
                  ),
                );

                // Close the menu
                _toggle();
              },
              backgroundColor: Theme.of(context).colorScheme.secondary,
              child: Icon(ToolIcons.getIconById(tool.id)),
            ),
          ),
        ],
      ),
    );
  }
}
