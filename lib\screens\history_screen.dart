import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:smart_toolbox/models/data_provider.dart';
import 'package:smart_toolbox/models/history_provider.dart';
import 'package:smart_toolbox/screens/tool_detail_screen.dart';
import 'package:smart_toolbox/utils/tool_icons.dart';
import 'package:intl/intl.dart';

class HistoryScreen extends StatelessWidget {
  const HistoryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final historyProvider = Provider.of<HistoryProvider>(context);
    final dataProvider = Provider.of<DataProvider>(context);
    final historyItems = historyProvider.historyItems;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Activity History'),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () {
              _showClearHistoryDialog(context, historyProvider);
            },
            tooltip: 'Clear History',
          ),
        ],
      ),
      body: historyProvider.isLoading
          ? const Center(child: CircularProgressIndicator())
          : historyItems.isEmpty
              ? _buildEmptyHistory(context)
              : ListView.builder(
                  itemCount: historyItems.length,
                  itemBuilder: (context, index) {
                    final historyItem = historyItems[index];
                    final tool = dataProvider.tools.firstWhere(
                      (tool) => tool.id == historyItem.toolId,
                      orElse: () => dataProvider.tools.first,
                    );
                    
                    // Group by date
                    final bool showDateHeader = index == 0 ||
                        !_isSameDay(
                          historyItems[index].timestamp,
                          historyItems[index - 1].timestamp,
                        );
                    
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (showDateHeader)
                          _buildDateHeader(context, historyItem.timestamp),
                        Dismissible(
                          key: Key(historyItem.id),
                          background: Container(
                            color: Colors.red,
                            alignment: Alignment.centerRight,
                            padding: const EdgeInsets.only(right: 20),
                            child: const Icon(
                              Icons.delete,
                              color: Colors.white,
                            ),
                          ),
                          direction: DismissDirection.endToStart,
                          onDismissed: (direction) {
                            historyProvider.removeHistoryItem(historyItem.id);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Removed from history'),
                                action: SnackBarAction(
                                  label: 'Undo',
                                  onPressed: () {
                                    // TODO: Implement undo functionality
                                  },
                                ),
                              ),
                            );
                          },
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: theme.colorScheme.primary.withAlpha(30),
                              child: Icon(
                                ToolIcons.getIconById(historyItem.toolId),
                                color: theme.colorScheme.primary,
                              ),
                            ),
                            title: Text(historyItem.toolName),
                            subtitle: Text(
                              '${_formatTime(historyItem.timestamp)} • ${historyItem.category}',
                            ),
                            trailing: Icon(Icons.chevron_right),
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ToolDetailScreen(tool: tool),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    );
                  },
                ),
    );
  }

  Widget _buildEmptyHistory(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Theme.of(context).colorScheme.primary.withAlpha(100),
          ),
          const SizedBox(height: 16),
          Text(
            'No Activity History',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Your tool usage history will appear here',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDateHeader(BuildContext context, DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateDay = DateTime(date.year, date.month, date.day);
    
    String headerText;
    if (dateDay.isAtSameMomentAs(DateTime(now.year, now.month, now.day))) {
      headerText = 'Today';
    } else if (dateDay.isAtSameMomentAs(yesterday)) {
      headerText = 'Yesterday';
    } else {
      headerText = DateFormat('EEEE, MMMM d').format(date);
    }
    
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        headerText,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    return DateFormat('h:mm a').format(dateTime);
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  void _showClearHistoryDialog(BuildContext context, HistoryProvider historyProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear History'),
        content: const Text(
          'Are you sure you want to clear your entire activity history? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              historyProvider.clearHistory();
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('History cleared')),
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}
