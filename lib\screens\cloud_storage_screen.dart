import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:smart_toolbox/models/cloud_storage_provider.dart';

class CloudStorageScreen extends StatelessWidget {
  const CloudStorageScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cloudProvider = Provider.of<CloudStorageProvider>(context);
    final selectedAccount = cloudProvider.selectedAccount;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cloud Storage'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Refresh files (in a real app)
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Refreshed files'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Account selector
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(20),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Storage Accounts',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 100,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: cloudProvider.accounts.length,
                    itemBuilder: (context, index) {
                      final account = cloudProvider.accounts[index];
                      final isSelected = selectedAccount?.id == account.id;
                      
                      return GestureDetector(
                        onTap: () {
                          if (account.status == CloudStorageStatus.connected) {
                            cloudProvider.selectAccount(account.id);
                          } else {
                            _showConnectDialog(context, account);
                          }
                        },
                        child: Container(
                          width: 120,
                          margin: const EdgeInsets.only(right: 16),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? theme.colorScheme.primary.withAlpha(30)
                                : theme.colorScheme.surface,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isSelected
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.outline.withAlpha(100),
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                _getStorageIcon(account.type),
                                color: isSelected
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.onSurface,
                                size: 32,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                account.name,
                                style: TextStyle(
                                  fontWeight: isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                  color: isSelected
                                      ? theme.colorScheme.primary
                                      : null,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                account.status == CloudStorageStatus.connected
                                    ? 'Connected'
                                    : 'Disconnected',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: account.status == CloudStorageStatus.connected
                                      ? Colors.green
                                      : Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                
                if (selectedAccount != null && selectedAccount.status == CloudStorageStatus.connected) ...[
                  const SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: selectedAccount.usagePercentage / 100,
                    backgroundColor: theme.colorScheme.primary.withAlpha(30),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      selectedAccount.usagePercentage > 90
                          ? Colors.red
                          : selectedAccount.usagePercentage > 70
                              ? Colors.orange
                              : theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    selectedAccount.formattedUsage,
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ],
            ),
          ),
          
          // Path navigation
          if (selectedAccount != null && selectedAccount.status == CloudStorageStatus.connected)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_upward),
                    onPressed: cloudProvider.currentPath == '/'
                        ? null
                        : () => cloudProvider.navigateUp(),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      cloudProvider.currentPath,
                      style: theme.textTheme.bodyMedium,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          
          // File list
          Expanded(
            child: selectedAccount == null || selectedAccount.status != CloudStorageStatus.connected
                ? Center(
                    child: Text(
                      'Connect to a cloud storage account to view files',
                      style: theme.textTheme.titleMedium,
                      textAlign: TextAlign.center,
                    ),
                  )
                : cloudProvider.files.isEmpty
                    ? Center(
                        child: Text(
                          'No files found',
                          style: theme.textTheme.titleMedium,
                        ),
                      )
                    : ListView.builder(
                        itemCount: cloudProvider.files.length,
                        itemBuilder: (context, index) {
                          final file = cloudProvider.files[index];
                          
                          return ListTile(
                            leading: Icon(
                              file.isDirectory
                                  ? Icons.folder
                                  : _getFileIcon(file.name),
                              color: file.isDirectory
                                  ? Colors.amber
                                  : theme.colorScheme.primary,
                            ),
                            title: Text(file.name),
                            subtitle: Text(
                              file.isDirectory
                                  ? file.formattedLastModified
                                  : '${file.formattedSize} • ${file.formattedLastModified}',
                            ),
                            onTap: file.isDirectory
                                ? () => cloudProvider.navigateToDirectory(file.path)
                                : () {
                                    // Show file details or actions
                                    _showFileActionsDialog(context, file);
                                  },
                          );
                        },
                      ),
          ),
        ],
      ),
      floatingActionButton: selectedAccount != null && selectedAccount.status == CloudStorageStatus.connected
          ? FloatingActionButton(
              onPressed: () {
                // Show upload options
                _showUploadOptionsDialog(context);
              },
              child: const Icon(Icons.upload_file),
            )
          : null,
    );
  }
  
  // Show connect dialog
  void _showConnectDialog(BuildContext context, CloudStorageAccount account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Connect to ${account.name}'),
        content: Text('Would you like to connect to ${account.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Provider.of<CloudStorageProvider>(context, listen: false)
                  .connectAccount(account.id);
            },
            child: const Text('Connect'),
          ),
        ],
      ),
    );
  }
  
  // Show file actions dialog
  void _showFileActionsDialog(BuildContext context, CloudFile file) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(150),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                file.name,
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            ListTile(
              leading: Icon(
                Icons.download,
                color: Theme.of(context).colorScheme.primary,
              ),
              title: const Text('Download'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Downloading ${file.name}...'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
            ListTile(
              leading: Icon(
                Icons.share,
                color: Theme.of(context).colorScheme.primary,
              ),
              title: const Text('Share'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Sharing ${file.name}...'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
            ListTile(
              leading: Icon(
                Icons.delete,
                color: Colors.red,
              ),
              title: const Text('Delete'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Deleting ${file.name}...'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
  
  // Show upload options dialog
  void _showUploadOptionsDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withAlpha(150),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Upload to Cloud',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ),
            ListTile(
              leading: Icon(
                Icons.file_upload,
                color: Theme.of(context).colorScheme.primary,
              ),
              title: const Text('Upload File'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Uploading file...'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
            ListTile(
              leading: Icon(
                Icons.create_new_folder,
                color: Theme.of(context).colorScheme.primary,
              ),
              title: const Text('Create Folder'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Creating folder...'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
  
  // Get storage icon
  IconData _getStorageIcon(CloudStorageType type) {
    switch (type) {
      case CloudStorageType.googleDrive:
        return Icons.cloud;
      case CloudStorageType.dropbox:
        return Icons.folder;
      case CloudStorageType.oneDrive:
        return Icons.cloud_circle;
      case CloudStorageType.local:
        return Icons.storage;
    }
  }
  
  // Get file icon
  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'mp3':
      case 'wav':
      case 'ogg':
        return Icons.audio_file;
      case 'mp4':
      case 'mov':
      case 'avi':
        return Icons.video_file;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.archive;
      default:
        return Icons.insert_drive_file;
    }
  }
}
