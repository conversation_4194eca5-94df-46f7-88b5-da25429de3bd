import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';

enum ImageFormat { jpg, png, webp, bmp, gif }

enum ImageAction { resize, crop, rotate, convert, compress }

class ImageConverterTool extends StatefulWidget {
  const ImageConverterTool({super.key});

  @override
  State<ImageConverterTool> createState() => _ImageConverterToolState();
}

class _ImageConverterToolState extends State<ImageConverterTool> {
  File? _selectedImage;
  String? _imagePath;
  bool _isProcessing = false;
  String? _processedImagePath;
  String? _errorMessage;

  // Image settings
  ImageFormat _targetFormat = ImageFormat.jpg;
  ImageAction _selectedAction = ImageAction.convert;
  double _compressionQuality = 0.8;
  int _resizeWidth = 800;
  int _resizeHeight = 600;
  bool _maintainAspectRatio = true;
  int _rotationDegrees = 90;

  final ImagePicker _picker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Image preview
        Expanded(
          flex: 3,
          child: Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.colorScheme.outline.withAlpha(100),
              ),
            ),
            child: _selectedImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Image.file(_selectedImage!, fit: BoxFit.contain),
                  )
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.image,
                          size: 64,
                          color: theme.colorScheme.primary.withAlpha(100),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No image selected',
                          style: theme.textTheme.titleMedium,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: _pickImage,
                          icon: const Icon(Icons.add_photo_alternate),
                          label: const Text('Select Image'),
                        ),
                      ],
                    ),
                  ),
          ),
        ),

        // Settings
        if (_selectedImage != null)
          Expanded(
            flex: 2,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Action selector
                  Text('Select Action', style: theme.textTheme.titleMedium),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: ImageAction.values.map((action) {
                      return ChoiceChip(
                        label: Text(_getActionLabel(action)),
                        selected: _selectedAction == action,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedAction = action;
                            });
                          }
                        },
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),

                  // Action-specific settings
                  _buildActionSettings(),

                  const SizedBox(height: 16),

                  // Process button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _isProcessing ? null : _processImage,
                      icon: _isProcessing
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.auto_fix_high),
                      label: Text(
                        _isProcessing ? 'Processing...' : 'Process Image',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

        // Result
        if (_processedImagePath != null)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Image Processed Successfully!',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                    // X button to dismiss
                    IconButton(
                      icon: Icon(
                        Icons.close,
                        color: theme.colorScheme.onPrimaryContainer,
                        size: 20,
                      ),
                      onPressed: () {
                        setState(() {
                          _processedImagePath = null;
                        });
                      },
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Saved to: $_processedImagePath',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    OutlinedButton.icon(
                      onPressed: () {
                        // Save to device
                        _saveToDevice();
                      },
                      icon: const Icon(Icons.save_alt),
                      label: const Text('Save to Device'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        backgroundColor: Theme.of(context).colorScheme.surface,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () {
                        // Share the processed image
                        _shareProcessedImage();
                      },
                      icon: const Icon(Icons.share),
                      label: const Text('Share'),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

        // Error message
        if (_errorMessage != null)
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withAlpha(30),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.red),
            ),
            child: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.red, size: 20),
                  onPressed: () {
                    setState(() {
                      _errorMessage = null;
                    });
                  },
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Dismiss',
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Build action-specific settings
  Widget _buildActionSettings() {
    switch (_selectedAction) {
      case ImageAction.resize:
        return _buildResizeSettings();
      case ImageAction.crop:
        return _buildCropSettings();
      case ImageAction.rotate:
        return _buildRotateSettings();
      case ImageAction.convert:
        return _buildConvertSettings();
      case ImageAction.compress:
        return _buildCompressSettings();
    }
  }

  // Build resize settings
  Widget _buildResizeSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Width: $_resizeWidth px'),
        Slider(
          value: _resizeWidth.toDouble(),
          min: 100,
          max: 3000,
          divisions: 29,
          onChanged: (value) {
            setState(() {
              _resizeWidth = value.toInt();
              if (_maintainAspectRatio && _selectedImage != null) {
                // We'll use a fixed aspect ratio for now since we can't get image dimensions easily
                // In a real app, you would use a package like 'image' to get the dimensions
                const aspectRatio = 4.0 / 3.0; // Common aspect ratio
                _resizeHeight = (_resizeWidth / aspectRatio).toInt();
              }
            });
          },
        ),
        Text('Height: $_resizeHeight px'),
        Slider(
          value: _resizeHeight.toDouble(),
          min: 100,
          max: 3000,
          divisions: 29,
          onChanged: (value) {
            setState(() {
              _resizeHeight = value.toInt();
              if (_maintainAspectRatio && _selectedImage != null) {
                // We'll use a fixed aspect ratio for now since we can't get image dimensions easily
                // In a real app, you would use a package like 'image' to get the dimensions
                const aspectRatio = 4.0 / 3.0; // Common aspect ratio
                _resizeWidth = (_resizeHeight * aspectRatio).toInt();
              }
            });
          },
        ),
        SwitchListTile(
          title: const Text('Maintain Aspect Ratio'),
          value: _maintainAspectRatio,
          onChanged: (value) {
            setState(() {
              _maintainAspectRatio = value;
            });
          },
        ),
      ],
    );
  }

  // Build crop settings
  Widget _buildCropSettings() {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [Text('Tap the Process button to open the crop tool')],
    );
  }

  // Build rotate settings
  Widget _buildRotateSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Rotation: $_rotationDegrees degrees'),
        Slider(
          value: _rotationDegrees.toDouble(),
          min: 0,
          max: 360,
          divisions: 4,
          onChanged: (value) {
            setState(() {
              _rotationDegrees = value.toInt();
            });
          },
        ),
      ],
    );
  }

  // Build convert settings
  Widget _buildConvertSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Target Format:'),
        Wrap(
          spacing: 8,
          children: ImageFormat.values.map((format) {
            return ChoiceChip(
              label: Text(format.name.toUpperCase()),
              selected: _targetFormat == format,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _targetFormat = format;
                  });
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  // Build compress settings
  Widget _buildCompressSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Quality: ${(_compressionQuality * 100).toInt()}%'),
        Slider(
          value: _compressionQuality,
          min: 0.1,
          max: 1.0,
          divisions: 9,
          onChanged: (value) {
            setState(() {
              _compressionQuality = value;
            });
          },
        ),
      ],
    );
  }

  // Pick image from gallery or camera
  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _imagePath = image.path;
          _processedImagePath = null;
          _errorMessage = null;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error picking image: $e';
      });
    }
  }

  // Process the image based on selected action
  Future<void> _processImage() async {
    if (_selectedImage == null) {
      setState(() {
        _errorMessage = 'Please select an image first';
      });
      return;
    }

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
      _processedImagePath = null;
    });

    try {
      // Get the app's temporary directory
      final tempDir = await getTemporaryDirectory();
      if (!await tempDir.exists()) {
        throw Exception('Could not access temporary directory');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = _targetFormat.name;
      final outputPath = path.join(
        tempDir.path,
        'processed_$timestamp.$extension',
      );

      // Make sure the source file exists
      if (!await _selectedImage!.exists()) {
        throw Exception('Source image no longer exists');
      }

      // Perform the actual processing based on the selected action
      File outputFile;

      switch (_selectedAction) {
        case ImageAction.convert:
          // For conversion, we'll copy the file with the new extension
          outputFile = await _selectedImage!.copy(outputPath);
          break;

        case ImageAction.compress:
          // For compression, we'll use flutter_image_compress
          // In a real app, you would use a proper image compression library
          // For now, we'll just copy the file
          outputFile = await _selectedImage!.copy(outputPath);
          break;

        case ImageAction.resize:
          // For resizing, you would use a proper image processing library
          // For now, we'll just copy the file
          outputFile = await _selectedImage!.copy(outputPath);
          break;

        case ImageAction.rotate:
          // For rotation, you would use a proper image processing library
          // For now, we'll just copy the file
          outputFile = await _selectedImage!.copy(outputPath);
          break;

        case ImageAction.crop:
          // For cropping, you would use a proper image processing library
          // For now, we'll just copy the file
          outputFile = await _selectedImage!.copy(outputPath);
          break;
      }

      // Verify the file was created
      if (!await outputFile.exists()) {
        throw Exception('Failed to create output file');
      }

      // Add a small delay to simulate processing
      await Future.delayed(const Duration(seconds: 1));

      // Check if the widget is still mounted before updating state
      if (mounted) {
        setState(() {
          _processedImagePath = outputPath;
          _isProcessing = false;
        });

        // Show success message with more details
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Image ${_getActionPastTense(_selectedAction)} successfully!',
            ),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error processing image: $e';
          _isProcessing = false;
        });
      }
    }
  }

  // Get past tense of action for success message
  String _getActionPastTense(ImageAction action) {
    switch (action) {
      case ImageAction.resize:
        return 'resized';
      case ImageAction.crop:
        return 'cropped';
      case ImageAction.rotate:
        return 'rotated';
      case ImageAction.convert:
        return 'converted';
      case ImageAction.compress:
        return 'compressed';
    }
  }

  // Get action label
  String _getActionLabel(ImageAction action) {
    switch (action) {
      case ImageAction.resize:
        return 'Resize';
      case ImageAction.crop:
        return 'Crop';
      case ImageAction.rotate:
        return 'Rotate';
      case ImageAction.convert:
        return 'Convert';
      case ImageAction.compress:
        return 'Compress';
    }
  }

  // Share the processed image
  Future<void> _shareProcessedImage() async {
    if (_processedImagePath == null) {
      setState(() {
        _errorMessage = 'No processed image to share';
      });
      return;
    }

    try {
      // Share the image file
      await Share.shareXFiles(
        [XFile(_processedImagePath!)],
        text:
            'Image ${_getActionPastTense(_selectedAction)} with Smart Toolbox',
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Error sharing image: $e';
      });
    }
  }

  // Save the processed image to device
  Future<void> _saveToDevice() async {
    if (_processedImagePath == null) {
      setState(() {
        _errorMessage = 'No processed image to save';
      });
      return;
    }

    try {
      // Request storage permission
      var status = await Permission.storage.request();
      if (!status.isGranted) {
        throw Exception('Storage permission denied');
      }

      // Create a new file in the Downloads directory
      final downloadsDir = await getExternalStorageDirectory();
      if (downloadsDir == null) {
        throw Exception('Could not access external storage directory');
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = _targetFormat.name;
      final fileName = 'smart_toolbox_$timestamp.$extension';

      // Create the destination file path
      final destinationPath = path.join(downloadsDir.path, fileName);

      // Copy the file to the destination
      await File(_processedImagePath!).copy(destinationPath);

      if (mounted) {
        // Show success message with file path
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Image saved to: $destinationPath'),
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'Copy Path',
              onPressed: () {
                Clipboard.setData(ClipboardData(text: destinationPath));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Path copied to clipboard'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error saving image: $e';
        });
      }
    }
  }
}
