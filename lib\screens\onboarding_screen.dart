import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smart_toolbox/screens/main_screen.dart';
import 'package:smart_toolbox/utils/page_transitions.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  
  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Welcome to Smart Toolbox',
      description: 'Your all-in-one utility toolkit with everything you need in one place.',
      image: 'assets/images/onboarding_welcome.png',
      backgroundColor: const Color(0xFF4A6FFF),
      textColor: Colors.white,
    ),
    OnboardingPage(
      title: 'Powerful Tools',
      description: 'Access over 50 powerful tools across 8 different categories.',
      image: 'assets/images/onboarding_tools.png',
      backgroundColor: const Color(0xFF00C8B8),
      textColor: Colors.white,
    ),
    OnboardingPage(
      title: 'Dark Mode',
      description: 'Switch between light and dark themes for comfortable usage day and night.',
      image: 'assets/images/onboarding_darkmode.png',
      backgroundColor: const Color(0xFF212529),
      textColor: Colors.white,
    ),
    OnboardingPage(
      title: 'Save Favorites',
      description: 'Mark your most used tools as favorites for quick access.',
      image: 'assets/images/onboarding_favorites.png',
      backgroundColor: const Color(0xFFFF6B6B),
      textColor: Colors.white,
    ),
  ];
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
  
  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }
  
  void _completeOnboarding() async {
    // Mark onboarding as completed
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_completed', true);
    
    if (!mounted) return;
    
    // Navigate to main screen
    Navigator.of(context).pushReplacement(
      PageTransitions.fadeTransition(
        page: const MainScreen(),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Page View
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: _pages.length,
            itemBuilder: (context, index) {
              final page = _pages[index];
              return Container(
                color: page.backgroundColor,
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Skip button (only on first pages)
                        if (index < _pages.length - 1)
                          Align(
                            alignment: Alignment.topRight,
                            child: TextButton(
                              onPressed: _completeOnboarding,
                              child: Text(
                                'Skip',
                                style: TextStyle(
                                  color: page.textColor.withAlpha(200),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        
                        const Spacer(),
                        
                        // Image
                        Image.asset(
                          page.image,
                          height: 240,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.image_not_supported_outlined,
                              size: 240,
                              color: page.textColor.withAlpha(100),
                            );
                          },
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Title
                        Text(
                          page.title,
                          style: TextStyle(
                            color: page.textColor,
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Description
                        Text(
                          page.description,
                          style: TextStyle(
                            color: page.textColor.withAlpha(220),
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const Spacer(),
                        
                        // Indicators
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(
                            _pages.length,
                            (index) => Container(
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              width: _currentPage == index ? 24 : 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: _currentPage == index
                                    ? page.textColor
                                    : page.textColor.withAlpha(100),
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Next/Get Started button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              if (_currentPage < _pages.length - 1) {
                                _pageController.nextPage(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                );
                              } else {
                                _completeOnboarding();
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: page.textColor,
                              foregroundColor: page.backgroundColor,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                            child: Text(
                              _currentPage < _pages.length - 1
                                  ? 'Next'
                                  : 'Get Started',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class OnboardingPage {
  final String title;
  final String description;
  final String image;
  final Color backgroundColor;
  final Color textColor;
  
  OnboardingPage({
    required this.title,
    required this.description,
    required this.image,
    required this.backgroundColor,
    required this.textColor,
  });
}
