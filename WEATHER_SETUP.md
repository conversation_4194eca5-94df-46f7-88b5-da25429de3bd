# Weather Tool Setup Instructions

The weather tool in this app requires an OpenWeatherMap API key to function properly.

## Getting Your Free API Key

1. Go to [OpenWeatherMap](https://openweathermap.org/api)
2. Click on "Sign Up" to create a free account
3. After signing up and verifying your email, go to your API keys section
4. Copy your API key

## Setting Up the API Key

1. Open the file `lib/tools/weather_tool.dart`
2. Find the line that says `const apiKey = 'YOUR_API_KEY_HERE';` (appears twice in the file)
3. Replace `YOUR_API_KEY_HERE` with your actual API key
4. Save the file

Example:
```dart
const apiKey = 'your_actual_api_key_here';
```

## Important Notes

- The free tier allows 1,000 API calls per day
- It may take up to 10 minutes for a new API key to become active
- Keep your API key secure and don't share it publicly
- For production apps, consider storing the API key in environment variables or secure storage

## Troubleshooting

If you're still getting errors after setting up the API key:

1. **"Invalid API key"** - Double-check that you copied the key correctly
2. **"Weather API key not configured"** - Make sure you replaced both instances of `YOUR_API_KEY_HERE`
3. **Network errors** - Check your internet connection
4. **Location errors** - Ensure location permissions are granted to the app

## Alternative Weather Services

If you prefer to use a different weather service, you can modify the weather tool to use:
- WeatherAPI.com
- AccuWeather API
- Weather Underground API

Just update the API endpoints and response parsing in the `_getWeather()` and `_getWeatherByCoordinates()` methods.
