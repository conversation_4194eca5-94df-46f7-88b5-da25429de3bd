
/// Represents a history item for a tool usage
class HistoryItem {
  final String id;
  final String toolId;
  final String toolName;
  final String category;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  HistoryItem({
    required this.id,
    required this.toolId,
    required this.toolName,
    required this.category,
    required this.timestamp,
    this.metadata = const {},
  });

  // Create a history item from JSON
  factory HistoryItem.fromJson(Map<String, dynamic> json) {
    return HistoryItem(
      id: json['id'] as String,
      toolId: json['toolId'] as String,
      toolName: json['toolName'] as String,
      category: json['category'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  // Convert history item to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'toolId': toolId,
      'toolName': toolName,
      'category': category,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  // Create a copy with updated fields
  HistoryItem copyWith({
    String? id,
    String? toolId,
    String? toolName,
    String? category,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return HistoryItem(
      id: id ?? this.id,
      toolId: toolId ?? this.toolId,
      toolName: toolName ?? this.toolName,
      category: category ?? this.category,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is HistoryItem &&
        other.id == id &&
        other.toolId == toolId &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode => id.hashCode ^ toolId.hashCode ^ timestamp.hashCode;
}
