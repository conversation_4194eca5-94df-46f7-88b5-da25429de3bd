import 'package:flutter/material.dart';
import 'package:smart_toolbox/models/category.dart';
import 'package:smart_toolbox/models/tool.dart';

class DataProvider with ChangeNotifier {
  // Categories
  final List<Category> _categories = [
    Category(
      id: 'media',
      name: 'Media Tools',
      iconPath: 'assets/icons/media.png',
      description: 'Tools for image and media editing',
      color: '#4A6FFF',
    ),
    Category(
      id: 'document',
      name: 'Document & Text Tools',
      iconPath: 'assets/icons/document.png',
      description: 'Tools for document and text processing',
      color: '#00C8B8',
    ),
    Category(
      id: 'developer',
      name: 'Developer Tools',
      iconPath: 'assets/icons/developer.png',
      description: 'Tools for developers and programmers',
      color: '#FF6B6B',
    ),
    Category(
      id: 'audio',
      name: 'Audio & Video Tools',
      iconPath: 'assets/icons/audio.png',
      description: 'Tools for audio and video processing',
      color: '#FFB400',
    ),
    Category(
      id: 'ai',
      name: 'AI Tools',
      iconPath: 'assets/icons/ai.png',
      description: 'AI-powered tools for various tasks',
      color: '#9C27B0',
    ),
    Category(
      id: 'productivity',
      name: 'Productivity Tools',
      iconPath: 'assets/icons/productivity.png',
      description: 'Tools to boost your productivity',
      color: '#4CAF50',
    ),
    Category(
      id: 'privacy',
      name: 'Privacy & Cleanup Tools',
      iconPath: 'assets/icons/privacy.png',
      description: 'Tools for privacy and cleanup',
      color: '#607D8B',
    ),
    Category(
      id: 'travel',
      name: 'Travel & Location Tools',
      iconPath: 'assets/icons/travel.png',
      description: 'Tools for travel and location',
      color: '#E91E63',
    ),
  ];

  // Tools
  final List<Tool> _tools = [
    // Media Tools
    Tool(
      id: 'remove_bg',
      name: 'Remove Image Background',
      description: 'Remove background from images with AI',
      iconPath: 'assets/icons/remove_bg.png',
      category: 'media',
    ),
    Tool(
      id: 'compress_images',
      name: 'Compress Images',
      description: 'Reduce image file size without losing quality',
      iconPath: 'assets/icons/compress.png',
      category: 'media',
    ),
    Tool(
      id: 'convert_image',
      name: 'Convert Image Format',
      description: 'Convert between JPG, PNG, WebP and more',
      iconPath: 'assets/icons/convert.png',
      category: 'media',
    ),
    Tool(
      id: 'crop_edit',
      name: 'Crop & Edit Images',
      description: 'Crop, resize and edit your images',
      iconPath: 'assets/icons/crop.png',
      category: 'media',
    ),
    Tool(
      id: 'enhance_image',
      name: 'Enhance Image Quality',
      description: 'Improve image quality with AI',
      iconPath: 'assets/icons/enhance.png',
      category: 'media',
      isPremium: true,
    ),
    Tool(
      id: 'photo_collage',
      name: 'Create Photo Collage',
      description: 'Create beautiful photo collages',
      iconPath: 'assets/icons/collage.png',
      category: 'media',
    ),
    Tool(
      id: 'images_to_pdf',
      name: 'Convert Images to PDF',
      description: 'Convert multiple images to a single PDF',
      iconPath: 'assets/icons/pdf.png',
      category: 'media',
    ),
    Tool(
      id: 'remove_exif',
      name: 'Remove EXIF Data',
      description: 'Remove metadata from your images',
      iconPath: 'assets/icons/exif.png',
      category: 'media',
    ),

    // Document & Text Tools
    Tool(
      id: 'pdf_reader',
      name: 'PDF Reader & Text Extractor',
      description: 'Read and extract text from PDF files',
      iconPath: 'assets/icons/pdf_reader.png',
      category: 'document',
    ),
    Tool(
      id: 'ocr',
      name: 'OCR: Image to Text',
      description: 'Extract text from images',
      iconPath: 'assets/icons/ocr.png',
      category: 'document',
    ),
    Tool(
      id: 'tts',
      name: 'Text-to-Speech',
      description: 'Convert text to natural-sounding speech',
      iconPath: 'assets/icons/tts.png',
      category: 'document',
    ),
    Tool(
      id: 'stt',
      name: 'Speech-to-Text',
      description: 'Convert speech to text',
      iconPath: 'assets/icons/stt.png',
      category: 'document',
    ),
    Tool(
      id: 'summarize',
      name: 'Summarize Text',
      description: 'Get AI-generated summaries of long texts',
      iconPath: 'assets/icons/summarize.png',
      category: 'document',
      isPremium: true,
    ),
    Tool(
      id: 'translate',
      name: 'Translate Text',
      description: 'Translate text between languages',
      iconPath: 'assets/icons/translate.png',
      category: 'document',
    ),
    Tool(
      id: 'signature',
      name: 'Electronic Signature Generator',
      description: 'Create and manage electronic signatures',
      iconPath: 'assets/icons/signature.png',
      category: 'document',
    ),
    Tool(
      id: 'table_to_excel',
      name: 'Convert Tables to Excel',
      description: 'Extract tables from images to Excel',
      iconPath: 'assets/icons/excel.png',
      category: 'document',
      isPremium: true,
    ),

    // Developer Tools
    Tool(
      id: 'json_formatter',
      name: 'JSON/XML Formatter',
      description: 'Format and validate JSON and XML data',
      iconPath: 'assets/icons/json.png',
      category: 'developer',
    ),
    Tool(
      id: 'code_editor',
      name: 'Live Code Editor',
      description: 'Edit and run code snippets in various languages',
      iconPath: 'assets/icons/code.png',
      category: 'developer',
      isPremium: true,
    ),
    Tool(
      id: 'qr_code',
      name: 'QR Code Generator',
      description: 'Generate and scan QR codes',
      iconPath: 'assets/icons/qr.png',
      category: 'developer',
    ),
    Tool(
      id: 'url_shortener',
      name: 'URL Shortener',
      description: 'Shorten long URLs for easier sharing',
      iconPath: 'assets/icons/url.png',
      category: 'developer',
    ),
    Tool(
      id: 'encoder',
      name: 'Text Encoder/Decoder',
      description: 'Encode and decode text in various formats',
      iconPath: 'assets/icons/encoder.png',
      category: 'developer',
    ),
    Tool(
      id: 'password',
      name: 'Password Generator',
      description: 'Generate strong, secure passwords',
      iconPath: 'assets/icons/password.png',
      category: 'developer',
    ),
    Tool(
      id: 'unit_converter',
      name: 'Unit Converter',
      description: 'Convert between different units of measurement',
      iconPath: 'assets/icons/converter.png',
      category: 'developer',
    ),

    // Audio & Video Tools
    Tool(
      id: 'video_to_mp3',
      name: 'Video to MP3 Converter',
      description: 'Extract audio from video files',
      iconPath: 'assets/icons/mp3.png',
      category: 'audio',
    ),
    Tool(
      id: 'video_trim',
      name: 'Trim or Mute Video',
      description: 'Trim video clips or remove audio',
      iconPath: 'assets/icons/trim.png',
      category: 'audio',
    ),
    Tool(
      id: 'voice_notes',
      name: 'Voice Notes',
      description: 'Record and organize voice notes',
      iconPath: 'assets/icons/voice.png',
      category: 'audio',
    ),
    Tool(
      id: 'speed_control',
      name: 'Audio Speed Control',
      description: 'Adjust playback speed of audio files',
      iconPath: 'assets/icons/speed.png',
      category: 'audio',
    ),
    Tool(
      id: 'ai_audio',
      name: 'AI Audio Summaries',
      description: 'Generate summaries from audio content',
      iconPath: 'assets/icons/ai_audio.png',
      category: 'audio',
      isPremium: true,
    ),

    // AI Tools
    Tool(
      id: 'email_assistant',
      name: 'Email Writing Assistant',
      description: 'AI-powered email writing and editing',
      iconPath: 'assets/icons/email.png',
      category: 'ai',
      isPremium: true,
    ),
    Tool(
      id: 'hashtag',
      name: 'Auto Hashtag Generator',
      description: 'Generate relevant hashtags for social media',
      iconPath: 'assets/icons/hashtag.png',
      category: 'ai',
    ),
    Tool(
      id: 'text_to_image',
      name: 'Text to Image',
      description: 'Generate images from text descriptions',
      iconPath: 'assets/icons/text_to_image.png',
      category: 'ai',
      isPremium: true,
    ),
    Tool(
      id: 'homework',
      name: 'Homework Helper',
      description: 'Get help with math, code, and more',
      iconPath: 'assets/icons/homework.png',
      category: 'ai',
    ),
    Tool(
      id: 'caption',
      name: 'AI Image Caption Generator',
      description: 'Generate captions for your images',
      iconPath: 'assets/icons/caption.png',
      category: 'ai',
    ),

    // Productivity Tools
    Tool(
      id: 'todo',
      name: 'To-Do Task Manager',
      description: 'Organize and track your tasks',
      iconPath: 'assets/icons/todo.png',
      category: 'productivity',
    ),
    Tool(
      id: 'pomodoro',
      name: 'Pomodoro Timer',
      description: 'Boost productivity with timed work sessions',
      iconPath: 'assets/icons/pomodoro.png',
      category: 'productivity',
    ),
    Tool(
      id: 'calculator',
      name: 'Scientific Calculator',
      description: 'Perform complex calculations',
      iconPath: 'assets/icons/calculator.png',
      category: 'productivity',
    ),
    Tool(
      id: 'scanner',
      name: 'Document Scanner',
      description: 'Scan documents with your camera',
      iconPath: 'assets/icons/scanner.png',
      category: 'productivity',
    ),
    Tool(
      id: 'text_note',
      name: 'Text Notes',
      description: 'Create and manage text notes',
      iconPath: 'assets/icons/todo.png',
      category: 'productivity',
    ),
    Tool(
      id: 'planner',
      name: 'Daily Planner',
      description: 'Plan your day efficiently',
      iconPath: 'assets/icons/planner.png',
      category: 'productivity',
      isPremium: true,
    ),

    // Privacy & Cleanup Tools
    Tool(
      id: 'exif_remover',
      name: 'EXIF Data Remover',
      description: 'Remove metadata from your files',
      iconPath: 'assets/icons/exif_remover.png',
      category: 'privacy',
    ),
    Tool(
      id: 'junk_cleaner',
      name: 'Junk File Cleaner',
      description: 'Clean up unnecessary files',
      iconPath: 'assets/icons/cleaner.png',
      category: 'privacy',
      isPremium: true,
    ),
    Tool(
      id: 'file_lock',
      name: 'File Lock with Password',
      description: 'Secure your files with passwords',
      iconPath: 'assets/icons/lock.png',
      category: 'privacy',
    ),
    Tool(
      id: 'usage_cleaner',
      name: 'App Usage Cleaner',
      description: 'Clear app usage history',
      iconPath: 'assets/icons/usage.png',
      category: 'privacy',
    ),

    // Travel & Location Tools
    Tool(
      id: 'currency',
      name: 'Currency Converter',
      description: 'Convert between different currencies',
      iconPath: 'assets/icons/currency.png',
      category: 'travel',
    ),
    Tool(
      id: 'timezone',
      name: 'Time Zone Converter',
      description: 'Convert times between different time zones',
      iconPath: 'assets/icons/timezone.png',
      category: 'travel',
    ),
    Tool(
      id: 'weather',
      name: 'Weather Forecast',
      description: 'Check weather conditions for any city',
      iconPath: 'assets/icons/distance.png',
      category: 'travel',
    ),
    Tool(
      id: 'distance',
      name: 'Distance Calculator',
      description: 'Calculate distances between locations',
      iconPath: 'assets/icons/distance.png',
      category: 'travel',
    ),
    Tool(
      id: 'camera_translate',
      name: 'Translate via Camera',
      description: 'Translate text using your camera',
      iconPath: 'assets/icons/camera_translate.png',
      category: 'travel',
      isPremium: true,
    ),
    Tool(
      id: 'business_card',
      name: 'Business Card Scanner',
      description: 'Scan and save business card information',
      iconPath: 'assets/icons/business_card.png',
      category: 'travel',
    ),
  ];

  // Getters
  List<Category> get categories => [..._categories];
  List<Tool> get tools => [..._tools];

  List<Tool> getToolsByCategory(String categoryId) {
    return _tools.where((tool) => tool.category == categoryId).toList();
  }

  List<Tool> get favoriteTools {
    return _tools.where((tool) => tool.isFavorite).toList();
  }

  // Methods
  void toggleFavorite(String toolId) {
    final toolIndex = _tools.indexWhere((tool) => tool.id == toolId);
    if (toolIndex >= 0) {
      _tools[toolIndex].isFavorite = !_tools[toolIndex].isFavorite;
      notifyListeners();
    }
  }
}
